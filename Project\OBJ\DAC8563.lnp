--cpu=Cortex-M4.fp.sp
".\obj\main.o"
".\obj\stm32f4xx_it.o"
".\obj\system_stm32f4xx.o"
".\obj\stm32f429_winner.o"
".\obj\delay.o"
".\obj\led.o"
".\obj\lcd.o"
".\obj\ltdc.o"
".\obj\sdram.o"
".\obj\usart.o"
".\obj\spi.o"
".\obj\key.o"
".\obj\dac8563.o"
".\obj\stm32f4xx_hal.o"
".\obj\stm32f4xx_hal_cortex.o"
".\obj\stm32f4xx_hal_gpio.o"
".\obj\stm32f4xx_hal_pwr.o"
".\obj\stm32f4xx_hal_pwr_ex.o"
".\obj\stm32f4xx_hal_rcc.o"
".\obj\stm32f4xx_hal_rcc_ex.o"
".\obj\stm32f4xx_hal_flash.o"
".\obj\stm32f4xx_hal_flash_ex.o"
".\obj\stm32f4xx_hal_flash_ramfunc.o"
".\obj\stm32f4xx_hal_ltdc.o"
".\obj\stm32f4xx_hal_ltdc_ex.o"
".\obj\stm32f4xx_hal_sdram.o"
".\obj\stm32f4xx_hal_sram.o"
".\obj\stm32f4xx_hal_tim.o"
".\obj\stm32f4xx_hal_tim_ex.o"
".\obj\stm32f4xx_hal_timebase_tim_template.o"
".\obj\stm32f4xx_hal_uart.o"
".\obj\stm32f4xx_hal_usart.o"
".\obj\stm32f4xx_ll_fmc.o"
".\obj\stm32f4xx_ll_fsmc.o"
".\obj\stm32f4xx_hal_dma.o"
".\obj\stm32f4xx_hal_dma_ex.o"
".\obj\stm32f4xx_hal_dma2d.o"
".\obj\stm32f4xx_hal_nor.o"
".\obj\stm32f4xx_hal_i2c.o"
".\obj\stm32f4xx_hal_i2c_ex.o"
".\obj\stm32f4xx_hal_spi.o"
".\obj\startup_stm32f429xx.o"
--strict --scatter ".\OBJ\DAC8563.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list ".\OBJ\DAC8563.map" -o .\OBJ\DAC8563.axf