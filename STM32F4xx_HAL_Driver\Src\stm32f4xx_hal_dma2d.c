/**
  ******************************************************************************
  * @file    stm32f4xx_hal_dma2d.c
  * <AUTHOR> Application Team
  * @version V1.4.4
  * @date    22-January-2016
  * @brief   DMA2D HAL module driver.
  *          This file provides firmware functions to manage the following 
  *          functionalities of the DMA2D peripheral:
  *           + Initialization and de-initialization functions
  *           + IO operation functions
  *           + Peripheral Control functions 
  *           + Peripheral State and Errors functions
  *
  @verbatim 
  ==============================================================================
                        ##### How to use this driver #####
  ==============================================================================
    [..]
      (#) Program the required configuration through following parameters:   
          the Transfer Mode, the output color mode and the output offset using 
          HAL_DMA2D_Init() function.

      (#) Program the required configuration through following parameters:   
          the input color mode, the input color, input alpha value, alpha mode 
          and the input offset using HAL_DMA2D_ConfigLayer() function for foreground
          or/and background layer.
          
     *** Polling mode IO operation ***
     =================================   
    [..]        
       (+) Configure the pdata, Destination and data length and Enable 
           the transfer using HAL_DMA2D_Start() 
       (+) Wait for end of transfer using HAL_DMA2D_PollForTransfer(), at this stage
           user can specify the value of timeout according to his end application.
               
     *** Interrupt mode IO operation ***    
     ===================================
     [..] 
       (#) Configure the pdata, Destination and data length and Enable 
           the transfer using HAL_DMA2D_Start_IT() 
       (#) Use HAL_DMA2D_IRQHandler() called under DMA2D_IRQHandler() Interrupt subroutine
       (#) At the end of data transfer HAL_DMA2D_IRQHandler() function is executed and user can 
           add his own function by customization of function pointer XferCpltCallback and 
           XferErrorCallback (i.e a member of DMA2D handle structure). 

         -@-   In Register-to-Memory transfer mode, the pdata parameter is the register
               color, in Memory-to-memory or memory-to-memory with pixel format
               conversion the pdata is the source address.

         -@-   Configure the foreground source address, the background source address, 
               the Destination and data length and Enable the transfer using 
               HAL_DMA2D_BlendingStart() in polling mode and HAL_DMA2D_BlendingStart_IT()
               in interrupt mode.
               
         -@-   HAL_DMA2D_BlendingStart() and HAL_DMA2D_BlendingStart_IT() functions
               are used if the memory to memory with blending transfer mode is selected.
                   
      (#) Optionally, configure and enable the CLUT using HAL_DMA2D_ConfigCLUT()
          HAL_DMA2D_EnableCLUT() functions.

      (#) Optionally, configure and enable LineInterrupt using the following function:
          HAL_DMA2D_ProgramLineEvent().
   
      (#) The transfer can be suspended, continued and aborted using the following
          functions: HAL_DMA2D_Suspend(), HAL_DMA2D_Resume(), HAL_DMA2D_Abort().
                     
      (#) To control DMA2D state you can use the following function: HAL_DMA2D_GetState()                   

     *** DMA2D HAL driver macros list ***
     ============================================= 
     [..]
       Below the list of most used macros in DMA2D HAL driver :
       
      (+) __HAL_DMA2D_ENABLE: Enable the DMA2D peripheral.
      (+) __HAL_DMA2D_DISABLE: Disable the DMA2D peripheral.
      (+) __HAL_DMA2D_GET_FLAG: Get the DMA2D pending flags.
      (+) __HAL_DMA2D_CLEAR_FLAG: Clear the DMA2D pending flags.
      (+) __HAL_DMA2D_ENABLE_IT: Enable the specified DMA2D interrupts.
      (+) __HAL_DMA2D_DISABLE_IT: Disable the specified DMA2D interrupts.
      (+) __HAL_DMA2D_GET_IT_SOURCE: Check whether the specified DMA2D interrupt has occurred or not.
     
     [..] 
      (@) You can refer to the DMA2D HAL driver header file for more useful macros
                                  
  @endverbatim
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; COPYRIGHT(c) 2016 STMicroelectronics</center></h2>
  *
  * Redistribution and use in source and binary forms, with or without modification,
  * are permitted provided that the following conditions are met:
  *   1. Redistributions of source code must retain the above copyright notice,
  *      this list of conditions and the following disclaimer.
  *   2. Redistributions in binary form must reproduce the above copyright notice,
  *      this list of conditions and the following disclaimer in the documentation
  *      and/or other materials provided with the distribution.
  *   3. Neither the name of STMicroelectronics nor the names of its contributors
  *      may be used to endorse or promote products derived from this software
  *      without specific prior written permission.
  *
  * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
  * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
  * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
  * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
  * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
  * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
  * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
  * CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
  * OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
  * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
  *
  ******************************************************************************
  */ 

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx_hal.h"

/** @addtogroup STM32F4xx_HAL_Driver
  * @{
  */
/** @addtogroup DMA2D
  * @brief DMA2D HAL module driver
  * @{
  */

#ifdef HAL_DMA2D_MODULE_ENABLED

#if defined(STM32F427xx) || defined(STM32F437xx) || defined(STM32F429xx) || defined(STM32F439xx) || defined(STM32F469xx) || defined(STM32F479xx)

/* Private types -------------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/** @addtogroup DMA2D_Private_Defines
  * @{
  */
#define HAL_TIMEOUT_DMA2D_ABORT      ((uint32_t)1000U)  /* 1s  */
#define HAL_TIMEOUT_DMA2D_SUSPEND    ((uint32_t)1000U)  /* 1s  */
/**
  * @}
  */

/* Private variables ---------------------------------------------------------*/
/* Private constants ---------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/
/** @addtogroup DMA2D_Private_Functions_Prototypes
  * @{
  */
static void DMA2D_SetConfig(DMA2D_HandleTypeDef *hdma2d, uint32_t pdata, uint32_t DstAddress, uint32_t Width, uint32_t Height);
/**
  * @}
  */

/* Private functions ---------------------------------------------------------*/
/* Exported functions --------------------------------------------------------*/
/** @addtogroup DMA2D_Exported_Functions
  * @{
  */

/** @defgroup DMA2D_Group1 Initialization and Configuration functions
 *  @brief   Initialization and Configuration functions
 *
@verbatim   
 ===============================================================================
                ##### Initialization and Configuration functions #####
 ===============================================================================  
    [..]  This section provides functions allowing to:
      (+) Initialize and configure the DMA2D
      (+) De-initialize the DMA2D 

@endverbatim
  * @{
  */
    
/**
  * @brief  Initializes the DMA2D according to the specified
  *         parameters in the DMA2D_InitTypeDef and create the associated handle.
  * @param  hdma2d: pointer to a DMA2D_HandleTypeDef structure that contains
  *                 the configuration information for the DMA2D.
  * @retval HAL status
  */
HAL_StatusTypeDef HAL_DMA2D_Init(DMA2D_HandleTypeDef *hdma2d)
{ 
  uint32_t tmp = 0U;

  /* Check the DMA2D peripheral state */
  if(hdma2d == NULL)
  {
     return HAL_ERROR;
  }

  /* Check the parameters */
  assert_param(IS_DMA2D_ALL_INSTANCE(hdma2d->Instance));
  assert_param(IS_DMA2D_MODE(hdma2d->Init.Mode));
  assert_param(IS_DMA2D_CMODE(hdma2d->Init.ColorMode));
  assert_param(IS_DMA2D_OFFSET(hdma2d->Init.OutputOffset));

  if(hdma2d->State == HAL_DMA2D_STATE_RESET)
  {
    /* Allocate lock resource and initialize it */
    hdma2d->Lock = HAL_UNLOCKED;
    /* Init the low level hardware */
    HAL_DMA2D_MspInit(hdma2d);
  }
  
  /* Change DMA2D peripheral state */
  hdma2d->State = HAL_DMA2D_STATE_BUSY;  

/* DMA2D CR register configuration -------------------------------------------*/
  /* Get the CR register value */
  tmp = hdma2d->Instance->CR;

  /* Clear Mode bits */
  tmp &= (uint32_t)~DMA2D_CR_MODE;

  /* Prepare the value to be wrote to the CR register */
  tmp |= hdma2d->Init.Mode;

  /* Write to DMA2D CR register */
  hdma2d->Instance->CR = tmp;

/* DMA2D OPFCCR register configuration ---------------------------------------*/
  /* Get the OPFCCR register value */
  tmp = hdma2d->Instance->OPFCCR;

  /* Clear Color Mode bits */
  tmp &= (uint32_t)~DMA2D_OPFCCR_CM;

  /* Prepare the value to be wrote to the OPFCCR register */
  tmp |= hdma2d->Init.ColorMode;

  /* Write to DMA2D OPFCCR register */
  hdma2d->Instance->OPFCCR = tmp;

/* DMA2D OOR register configuration ------------------------------------------*/  
  /* Get the OOR register value */
  tmp = hdma2d->Instance->OOR;

  /* Clear Offset bits */
  tmp &= (uint32_t)~DMA2D_OOR_LO;

  /* Prepare the value to be wrote to the OOR register */
  tmp |= hdma2d->Init.OutputOffset;

  /* Write to DMA2D OOR register */
  hdma2d->Instance->OOR = tmp;

  /* Update error code */
  hdma2d->ErrorCode = HAL_DMA2D_ERROR_NONE;

  /* Initialize the DMA2D state*/
  hdma2d->State  = HAL_DMA2D_STATE_READY;

  return HAL_OK;
}

/**
  * @brief  Deinitializes the DMA2D peripheral registers to their default reset
  *         values.
  * @param  hdma2d: pointer to a DMA2D_HandleTypeDef structure that contains
  *                 the configuration information for the DMA2D.
  * @retval None
  */

HAL_StatusTypeDef HAL_DMA2D_DeInit(DMA2D_HandleTypeDef *hdma2d)
{
  /* Check the DMA2D peripheral state */
  if(hdma2d == NULL)
  {
     return HAL_ERROR;
  }

  /* DeInit the low level hardware */
  HAL_DMA2D_MspDeInit(hdma2d);

  /* Update error code */
  hdma2d->ErrorCode = HAL_DMA2D_ERROR_NONE;

  /* Initialize the DMA2D state*/
  hdma2d->State  = HAL_DMA2D_STATE_RESET;

  /* Release Lock */
  __HAL_UNLOCK(hdma2d);

  return HAL_OK;
}

/**
  * @brief  Initializes the DMA2D MSP.
  * @param  hdma2d: pointer to a DMA2D_HandleTypeDef structure that contains
  *                 the configuration information for the DMA2D.
  * @retval None
  */
__weak void HAL_DMA2D_MspInit(DMA2D_HandleTypeDef* hdma2d)
{
  /* Prevent unused argument(s) compilation warning */
  UNUSED(hdma2d);
  /* NOTE : This function Should not be modified, when the callback is needed,
            the HAL_DMA2D_MspInit could be implemented in the user file
   */ 
}

/**
  * @brief  DeInitializes the DMA2D MSP.
  * @param  hdma2d: pointer to a DMA2D_HandleTypeDef structure that contains
  *                 the configuration information for the DMA2D.
  * @retval None
  */
__weak void HAL_DMA2D_MspDeInit(DMA2D_HandleTypeDef* hdma2d)
{
  /* Prevent unused argument(s) compilation warning */
  UNUSED(hdma2d);
  /* NOTE : This function Should not be modified, when the callback is needed,
            the HAL_DMA2D_MspDeInit could be implemented in the user file
   */ 
}

/**
  * @}
  */

/** @defgroup DMA2D_Group2 IO operation functions 
 *  @brief   IO operation functions  
 *
@verbatim   
 ===============================================================================
                      #####  IO operation functions  #####
 ===============================================================================  
    [..]  This section provides functions allowing to:
      (+) Configure the pdata, destination address and data size and 
          Start DMA2D transfer.
      (+) Configure the source for foreground and background, destination address 
          and data size and Start MultiBuffer DMA2D transfer.
      (+) Configure the pdata, destination address and data size and 
          Start DMA2D transfer with interrupt.
      (+) Configure the source for foreground and background, destination address 
          and data size and Start MultiBuffer DMA2D transfer with interrupt.
      (+) Abort DMA2D transfer.
      (+) Suspend DMA2D transfer.
      (+) Continue DMA2D transfer.
      (+) Configure CLUT Loading with interrupt enabled
      (+) Poll for transfer complete.
      (+) handle DMA2D interrupt request.
      (+) Transfer watermark callback.
      (+) CLUT Transfer Complete callback.
        
@endverbatim
  * @{
  */

/**
  * @brief  Start the DMA2D Transfer.
  * @param  hdma2d:     pointer to a DMA2D_HandleTypeDef structure that contains
  *                     the configuration information for the DMA2D.  
  * @param  pdata:      Configure the source memory Buffer address if 
  *                     the memory to memory or memory to memory with pixel format 
  *                     conversion DMA2D mode is selected, and configure 
  *                     the color value if register to memory DMA2D mode is selected.
  * @param  DstAddress: The destination memory Buffer address.
  * @param  Width:      The width of data to be transferred from source to destination.
  * @param  Height:      The height of data to be transferred from source to destination.
  * @retval HAL status
  */
HAL_StatusTypeDef HAL_DMA2D_Start(DMA2D_HandleTypeDef *hdma2d, uint32_t pdata, uint32_t DstAddress, uint32_t Width,  uint32_t Height)
{
  /* Check the parameters */
  assert_param(IS_DMA2D_LINE(Height));
  assert_param(IS_DMA2D_PIXEL(Width));
  
  /* Process locked */
  __HAL_LOCK(hdma2d);

  /* Change DMA2D peripheral state */
  hdma2d->State = HAL_DMA2D_STATE_BUSY;

  /* Configure the source, destination address and the data size */
  DMA2D_SetConfig(hdma2d, pdata, DstAddress, Width, Height);

  /* Enable the Peripheral */
  __HAL_DMA2D_ENABLE(hdma2d);

  return HAL_OK;
}

/**
  * @brief  Start the DMA2D Transfer with interrupt enabled.
  * @param  hdma2d:     pointer to a DMA2D_HandleTypeDef structure that contains
  *                     the configuration information for the DMA2D.  
  * @param  pdata:      Configure the source memory Buffer address if 
  *                     the memory to memory or memory to memory with pixel format 
  *                     conversion DMA2D mode is selected, and configure 
  *                     the color value if register to memory DMA2D mode is selected.
  * @param  DstAddress: The destination memory Buffer address.
  * @param  Width:      The width of data to be transferred from source to destination.
  * @param  Height:     The height of data to be transferred from source to destination.
  * @retval HAL status
  */
HAL_StatusTypeDef HAL_DMA2D_Start_IT(DMA2D_HandleTypeDef *hdma2d, uint32_t pdata, uint32_t DstAddress, uint32_t Width,  uint32_t Height)
{  
  /* Check the parameters */
  assert_param(IS_DMA2D_LINE(Height));
  assert_param(IS_DMA2D_PIXEL(Width));
  
  /* Process locked */
  __HAL_LOCK(hdma2d);

  /* Change DMA2D peripheral state */
  hdma2d->State = HAL_DMA2D_STATE_BUSY;

  /* Configure the source, destination address and the data size */
  DMA2D_SetConfig(hdma2d, pdata, DstAddress, Width, Height);

 /* Enable the transfer complete,transfer Error and configuration error interrupts */
  __HAL_DMA2D_ENABLE_IT(hdma2d, DMA2D_IT_TC | DMA2D_IT_TE | DMA2D_IT_CE);

  /* Enable the Peripheral */
  __HAL_DMA2D_ENABLE(hdma2d);

  return HAL_OK;
}

/**
  * @brief  Start the multi-source DMA2D Transfer.
  * @param  hdma2d:      pointer to a DMA2D_HandleTypeDef structure that contains
  *                      the configuration information for the DMA2D.  
  * @param  SrcAddress1: The source memory Buffer address of the foreground layer.
  * @param  SrcAddress2: The source memory Buffer address of the background layer.
  * @param  DstAddress:  The destination memory Buffer address
  * @param  Width:       The width of data to be transferred from source to destination.
  * @param  Height:      The height of data to be transferred from source to destination.
  * @retval HAL status
  */
HAL_StatusTypeDef HAL_DMA2D_BlendingStart(DMA2D_HandleTypeDef *hdma2d, uint32_t SrcAddress1, uint32_t  SrcAddress2, uint32_t DstAddress, uint32_t Width,  uint32_t Height)
{
  /* Check the parameters */
  assert_param(IS_DMA2D_LINE(Height));
  assert_param(IS_DMA2D_PIXEL(Width));
  
  /* Process locked */
  __HAL_LOCK(hdma2d);

  /* Change DMA2D peripheral state */
  hdma2d->State = HAL_DMA2D_STATE_BUSY; 

  /* Configure DMA2D Stream source2 address */
  hdma2d->Instance->BGMAR = SrcAddress2;

  /* Configure the source, destination address and the data size */
  DMA2D_SetConfig(hdma2d, SrcAddress1, DstAddress, Width, Height);

  /* Enable the Peripheral */
  __HAL_DMA2D_ENABLE(hdma2d);

  return HAL_OK;
}

/**
  * @brief  Start the multi-source DMA2D Transfer with interrupt enabled.
  * @param  hdma2d:     pointer to a DMA2D_HandleTypeDef structure that contains
  *                     the configuration information for the DMA2D.  
  * @param  SrcAddress1: The source memory Buffer address of the foreground layer.
  * @param  SrcAddress2: The source memory Buffer address of the background layer.
  * @param  DstAddress:  The destination memory Buffer address.
  * @param  Width:       The width of data to be transferred from source to destination.
  * @param  Height:      The height of data to be transferred from source to destination.
  * @retval HAL status
  */
HAL_StatusTypeDef HAL_DMA2D_BlendingStart_IT(DMA2D_HandleTypeDef *hdma2d, uint32_t SrcAddress1, uint32_t  SrcAddress2, uint32_t DstAddress, uint32_t Width,  uint32_t Height)
{
  /* Check the parameters */
  assert_param(IS_DMA2D_LINE(Height));
  assert_param(IS_DMA2D_PIXEL(Width));
  
  /* Process locked */
  __HAL_LOCK(hdma2d);

  /* Change DMA2D peripheral state */
  hdma2d->State = HAL_DMA2D_STATE_BUSY;
 
  /* Configure DMA2D Stream source2 address */
  hdma2d->Instance->BGMAR = SrcAddress2;

  /* Configure the source, destination address and the data size */
  DMA2D_SetConfig(hdma2d, SrcAddress1, DstAddress, Width, Height);

  /* Enable the transfer complete,transfer Error and configuration error interrupts */
  __HAL_DMA2D_ENABLE_IT(hdma2d, DMA2D_IT_TC | DMA2D_IT_TE | DMA2D_IT_CE);

  /* Enable the Peripheral */
  __HAL_DMA2D_ENABLE(hdma2d);

  return HAL_OK;
}

/**
  * @brief  Abort the DMA2D Transfer.
  * @param  hdma2d : pointer to a DMA2D_HandleTypeDef structure that contains
  *                  the configuration information for the DMA2D.
  * @retval HAL status
  */
HAL_StatusTypeDef HAL_DMA2D_Abort(DMA2D_HandleTypeDef *hdma2d)
{
  uint32_t tickstart = 0U;
  uint32_t regValue; 

  /* Abort the current DMA2D transfer */
  /* Calculate the new CR register value : Reset START bit and Set Abort bit*/
  regValue = (hdma2d->Instance->CR & (~DMA2D_CR_START)) | DMA2D_CR_ABORT;
  hdma2d->Instance->CR = regValue;

  /* Get tick */
  tickstart = HAL_GetTick();

  /* Check if the DMA2D is effectively disabled */
  while((hdma2d->Instance->CR & DMA2D_CR_START) != 0U)
  {
    if((HAL_GetTick() - tickstart ) > HAL_TIMEOUT_DMA2D_ABORT)
    {
      /* Update error code */
      hdma2d->ErrorCode |= HAL_DMA2D_ERROR_TIMEOUT;
      
      /* Change the DMA2D state */
      hdma2d->State= HAL_DMA2D_STATE_TIMEOUT;
      
      /* Process Unlocked */
      __HAL_UNLOCK(hdma2d);
      
      return HAL_TIMEOUT;
    }
  }
  /* Process Unlocked */
  __HAL_UNLOCK(hdma2d);

  /* Change the DMA2D state*/
  hdma2d->State = HAL_DMA2D_STATE_READY;

  return HAL_OK;
}

/**
  * @brief  Suspend the DMA2D Transfer.
  * @param  hdma2d: pointer to a DMA2D_HandleTypeDef structure that contains
  *                 the configuration information for the DMA2D. 
  * @retval HAL status
  */
HAL_StatusTypeDef HAL_DMA2D_Suspend(DMA2D_HandleTypeDef *hdma2d)
{
  uint32_t tickstart = 0U;

  /* Suspend the DMA2D transfer */
  MODIFY_REG(hdma2d->Instance->CR, DMA2D_CR_SUSP|DMA2D_CR_START, DMA2D_CR_SUSP);

  /* Get tick */
  tickstart = HAL_GetTick();

  /* Check if the DMA2D is effectively suspended */
  while(((hdma2d->Instance->CR & DMA2D_CR_SUSP) != DMA2D_CR_SUSP) && \
        ((hdma2d->Instance->CR & DMA2D_CR_START) == DMA2D_CR_START))        
  {
    if((HAL_GetTick() - tickstart ) > HAL_TIMEOUT_DMA2D_SUSPEND)
    {
      /* Update error code */
      hdma2d->ErrorCode |= HAL_DMA2D_ERROR_TIMEOUT;
      
      /* Change the DMA2D state */
      hdma2d->State= HAL_DMA2D_STATE_TIMEOUT;
      
      return HAL_TIMEOUT;
    }
  }
  /* Check whether or not a transfer is actually suspended and change the DMA2D state accordingly */
  if ((hdma2d->Instance->CR & DMA2D_CR_START) != 0U)
  {	  
    hdma2d->State = HAL_DMA2D_STATE_SUSPEND;
  }

  return HAL_OK;
}

/**
  * @brief  Resume the DMA2D Transfer.
  * @param  hdma2d: pointer to a DMA2D_HandleTypeDef structure that contains
  *                 the configuration information for the DMA2D.  
  * @retval HAL status
  */
HAL_StatusTypeDef HAL_DMA2D_Resume(DMA2D_HandleTypeDef *hdma2d)
{
  /* Check the suspend bit and START bit */
  if((hdma2d->Instance->CR & (DMA2D_CR_SUSP | DMA2D_CR_START)) == (DMA2D_CR_SUSP | DMA2D_CR_START))
  {
    /* ongoing transfer is suspended :  Change the DMA2D state before resuming*/
    hdma2d->State = HAL_DMA2D_STATE_BUSY;
  }

  /* Resume the DMA2D transfer */
  CLEAR_BIT(hdma2d->Instance->CR, (DMA2D_CR_SUSP|DMA2D_CR_START));  
  
  return HAL_OK;  
}

/**
  * @brief  Start DMA2D CLUT Loading with interrupt enabled.
  * @param  hdma2d:   pointer to a DMA2D_HandleTypeDef structure that contains
  *                   the configuration information for the DMA2D.
  * @param  CLUTCfg:  pointer to a DMA2D_CLUTCfgTypeDef structure that contains
  *                   the configuration information for the color look up table.
  * @param  LayerIdx: DMA2D Layer index.
  *                   This parameter can be one of the following values:
  *                   0(background) / 1(foreground)
  * @retval HAL status
  */
HAL_StatusTypeDef HAL_DMA2D_CLUTLoad_IT(DMA2D_HandleTypeDef *hdma2d, DMA2D_CLUTCfgTypeDef CLUTCfg, uint32_t LayerIdx)
{
  /* Check the parameters */
  assert_param(IS_DMA2D_LAYER(LayerIdx));   
  assert_param(IS_DMA2D_CLUT_CM(CLUTCfg.CLUTColorMode));
  assert_param(IS_DMA2D_CLUT_SIZE(CLUTCfg.Size));
  
  /* Process locked */
  __HAL_LOCK(hdma2d);
  
  /* Change DMA2D peripheral state */
  hdma2d->State = HAL_DMA2D_STATE_BUSY; 
  
  /* Configure the CLUT of the background DMA2D layer */
  if(LayerIdx == 0U)
  {
    /* Write to DMA2D BGCMAR register */
    hdma2d->Instance->BGCMAR = (uint32_t)CLUTCfg.pCLUT;
    
   /* Write background CLUT size and CLUT color mode */
    MODIFY_REG(hdma2d->Instance->BGPFCCR, (DMA2D_BGPFCCR_CS | DMA2D_BGPFCCR_CCM), ((CLUTCfg.Size << 8U) | (CLUTCfg.CLUTColorMode << 4U)));     

    /* Enable the C-LUT Transfer Complete,transfer Error and C-LUT Access Error interrupts */
    __HAL_DMA2D_ENABLE_IT(hdma2d, DMA2D_IT_CTC | DMA2D_IT_TE | DMA2D_IT_CAE);
  
    /* Enable the CLUT loading for the background */
    hdma2d->Instance->BGPFCCR |= DMA2D_BGPFCCR_START;  
  }
  /* Configure the CLUT of the foreground DMA2D layer */
  else
  {
    /* Write to DMA2D FGCMAR register */
    hdma2d->Instance->FGCMAR = (uint32_t)CLUTCfg.pCLUT;
    
    /* Write foreground CLUT size and CLUT color mode */
    MODIFY_REG(hdma2d->Instance->FGPFCCR, (DMA2D_FGPFCCR_CS | DMA2D_FGPFCCR_CCM), ((CLUTCfg.Size << 8U) | (CLUTCfg.CLUTColorMode << 4U)));  

    /* Enable the C-LUT Transfer Complete,transfer Error and C-LUT Access Error interrupts */
    __HAL_DMA2D_ENABLE_IT(hdma2d, DMA2D_IT_CTC | DMA2D_IT_TE | DMA2D_IT_CAE);
    
     /* Enable the CLUT loading for the foreground */
    hdma2d->Instance->FGPFCCR |= DMA2D_FGPFCCR_START;   
  }
 
  
  return HAL_OK;
}

/**
  * @brief  Polling for transfer complete or CLUT loading.
  * @param  hdma2d: pointer to a DMA2D_HandleTypeDef structure that contains
  *                 the configuration information for the DMA2D. 
  * @param  Timeout: Timeout duration
  * @retval HAL status
  */
HAL_StatusTypeDef HAL_DMA2D_PollForTransfer(DMA2D_HandleTypeDef *hdma2d, uint32_t Timeout)
{
  uint32_t tickstart = 0U;

  /* Polling for DMA2D transfer */
  if((hdma2d->Instance->CR & DMA2D_CR_START) != 0U)
  {
   /* Get tick */
   tickstart = HAL_GetTick();

    while(__HAL_DMA2D_GET_FLAG(hdma2d, DMA2D_FLAG_TC) == RESET)
    {
      if((__HAL_DMA2D_GET_FLAG(hdma2d, DMA2D_FLAG_CE|DMA2D_FLAG_TE) != RESET))
      {
        if (__HAL_DMA2D_GET_FLAG(hdma2d, DMA2D_FLAG_CE) != RESET)
        {
          hdma2d->ErrorCode |= HAL_DMA2D_ERROR_CE;          
        }
        
        if (__HAL_DMA2D_GET_FLAG(hdma2d, DMA2D_FLAG_TE) != RESET)
        {
          hdma2d->ErrorCode |= HAL_DMA2D_ERROR_TE;          
        }        
        /* Clear the transfer and configuration error flags */
        __HAL_DMA2D_CLEAR_FLAG(hdma2d, DMA2D_FLAG_CE | DMA2D_FLAG_TE);

        /* Change DMA2D state */
        hdma2d->State= HAL_DMA2D_STATE_ERROR;

        /* Process unlocked */
        __HAL_UNLOCK(hdma2d);
        
        return HAL_ERROR;
      }
      /* Check for the Timeout */
      if(Timeout != HAL_MAX_DELAY)
      {
        if((Timeout == 0U)||((HAL_GetTick() - tickstart ) > Timeout))
        {
          /* Process unlocked */
          __HAL_UNLOCK(hdma2d);
        
          /* Update error code */
          hdma2d->ErrorCode |= HAL_DMA2D_ERROR_TIMEOUT;

          /* Change the DMA2D state */
          hdma2d->State= HAL_DMA2D_STATE_TIMEOUT;
          
          return HAL_TIMEOUT;
        }
      }        
    }
  }
  /* Polling for foreground CLUT loading */
  if((hdma2d->Instance->FGPFCCR & DMA2D_FGPFCCR_START) != 0U)
  {
    /* Get tick */
    tickstart = HAL_GetTick();
   
    while(__HAL_DMA2D_GET_FLAG(hdma2d, DMA2D_FLAG_CTC) == RESET)
    {
      if((__HAL_DMA2D_GET_FLAG(hdma2d, DMA2D_FLAG_CAE) != RESET))
      {      
        hdma2d->ErrorCode |= HAL_DMA2D_ERROR_CAE;
        
        /* Clear the CLUT Access Error flag */
        __HAL_DMA2D_CLEAR_FLAG(hdma2d, DMA2D_FLAG_CAE);
        
        /* Change DMA2D state */
        hdma2d->State= HAL_DMA2D_STATE_ERROR;
        
        return HAL_ERROR;      
      }      
      /* Check for the Timeout */
      if(Timeout != HAL_MAX_DELAY)
      {
        if((Timeout == 0U)||((HAL_GetTick() - tickstart ) > Timeout))
        {
          /* Update error code */
          hdma2d->ErrorCode |= HAL_DMA2D_ERROR_TIMEOUT;
    
          /* Change the DMA2D state */
          hdma2d->State= HAL_DMA2D_STATE_TIMEOUT;
          
          return HAL_TIMEOUT;
        }
      }      
    }
  }
  /* Polling for background CLUT loading */
  if((hdma2d->Instance->BGPFCCR & DMA2D_BGPFCCR_START) != 0U)
  {
    /* Get tick */
    tickstart = HAL_GetTick();
   
    while(__HAL_DMA2D_GET_FLAG(hdma2d, DMA2D_FLAG_CTC) == RESET)
    {
      if((__HAL_DMA2D_GET_FLAG(hdma2d, DMA2D_FLAG_CAE) != RESET))
      {
        hdma2d->ErrorCode |= HAL_DMA2D_ERROR_CAE;
        
        /* Clear the CLUT Access Error flag */
        __HAL_DMA2D_CLEAR_FLAG(hdma2d, DMA2D_FLAG_CAE);
        
        /* Change DMA2D state */
        hdma2d->State= HAL_DMA2D_STATE_ERROR;
        
        return HAL_ERROR;      
      }      
      /* Check for the Timeout */
      if(Timeout != HAL_MAX_DELAY)
      {
        if((Timeout == 0U)||((HAL_GetTick() - tickstart ) > Timeout))
        {
          /* Update error code */
          hdma2d->ErrorCode |= HAL_DMA2D_ERROR_TIMEOUT;
    
          /* Change the DMA2D state */
          hdma2d->State= HAL_DMA2D_STATE_TIMEOUT;
          
          return HAL_TIMEOUT;
        }
      }      
    }
  }  
  
  /* Clear the transfer complete and CLUT loading flags */
  __HAL_DMA2D_CLEAR_FLAG(hdma2d, DMA2D_FLAG_TC | DMA2D_FLAG_CTC); 
  
  /* Change DMA2D state */
  hdma2d->State = HAL_DMA2D_STATE_READY;
  
  /* Process unlocked */
  __HAL_UNLOCK(hdma2d);
  
  return HAL_OK;
}
/**
  * @brief  Handles DMA2D interrupt request.
  * @param  hdma2d: pointer to a DMA2D_HandleTypeDef structure that contains
  *                 the configuration information for the DMA2D.  
  * @retval HAL status
  */
void HAL_DMA2D_IRQHandler(DMA2D_HandleTypeDef *hdma2d)
{    
  /* Transfer Error Interrupt management ***************************************/
  if(__HAL_DMA2D_GET_FLAG(hdma2d, DMA2D_FLAG_TE) != RESET)
  {
    if(__HAL_DMA2D_GET_IT_SOURCE(hdma2d, DMA2D_IT_TE) != RESET)
    {
      /* Disable the transfer Error interrupt */
      __HAL_DMA2D_DISABLE_IT(hdma2d, DMA2D_IT_TE);  

      /* Update error code */
      hdma2d->ErrorCode |= HAL_DMA2D_ERROR_TE;
    
      /* Clear the transfer error flag */
      __HAL_DMA2D_CLEAR_FLAG(hdma2d, DMA2D_FLAG_TE);

      /* Change DMA2D state */
      hdma2d->State = HAL_DMA2D_STATE_ERROR;

      /* Process Unlocked */
      __HAL_UNLOCK(hdma2d);       
      
      if(hdma2d->XferErrorCallback != NULL)
      {
        /* Transfer error Callback */
        hdma2d->XferErrorCallback(hdma2d);
      }
    }
  }
  /* Configuration Error Interrupt management **********************************/
  if(__HAL_DMA2D_GET_FLAG(hdma2d, DMA2D_FLAG_CE) != RESET)
  {
    if(__HAL_DMA2D_GET_IT_SOURCE(hdma2d, DMA2D_IT_CE) != RESET)
    {
      /* Disable the Configuration Error interrupt */
      __HAL_DMA2D_DISABLE_IT(hdma2d, DMA2D_IT_CE);
  
      /* Clear the Configuration error flag */
      __HAL_DMA2D_CLEAR_FLAG(hdma2d, DMA2D_FLAG_CE);

      /* Update error code */
      hdma2d->ErrorCode |= HAL_DMA2D_ERROR_CE;    
    
      /* Change DMA2D state */
      hdma2d->State = HAL_DMA2D_STATE_ERROR;

      /* Process Unlocked */
      __HAL_UNLOCK(hdma2d);       
      
      if(hdma2d->XferErrorCallback != NULL)
      {
        /* Transfer error Callback */
        hdma2d->XferErrorCallback(hdma2d);
      }
    }
  }
  /* CLUT access Error Interrupt management ***********************************/
  if(__HAL_DMA2D_GET_FLAG(hdma2d, DMA2D_FLAG_CAE) != RESET)
  {
    if(__HAL_DMA2D_GET_IT_SOURCE(hdma2d, DMA2D_IT_CAE) != RESET)
    {
      /* Disable the CLUT access error interrupt */
      __HAL_DMA2D_DISABLE_IT(hdma2d, DMA2D_IT_CAE);
  
      /* Clear the CLUT access error flag */
      __HAL_DMA2D_CLEAR_FLAG(hdma2d, DMA2D_FLAG_CAE);

      /* Update error code */
      hdma2d->ErrorCode |= HAL_DMA2D_ERROR_CAE;    
    
      /* Change DMA2D state */
      hdma2d->State = HAL_DMA2D_STATE_ERROR;

      /* Process Unlocked */
      __HAL_UNLOCK(hdma2d);       
      
      if(hdma2d->XferErrorCallback != NULL)
      {
        /* Transfer error Callback */
        hdma2d->XferErrorCallback(hdma2d);
      }
    }
  }  
  /* Transfer watermark Interrupt management **********************************/
  if(__HAL_DMA2D_GET_FLAG(hdma2d, DMA2D_FLAG_TW) != RESET)
  {
    if(__HAL_DMA2D_GET_IT_SOURCE(hdma2d, DMA2D_IT_TW) != RESET)
    { 
      /* Disable the transfer watermark interrupt */
      __HAL_DMA2D_DISABLE_IT(hdma2d, DMA2D_IT_TW);
  
      /* Clear the transfer watermark flag */  
      __HAL_DMA2D_CLEAR_FLAG(hdma2d, DMA2D_FLAG_TW);

      /* Transfer watermark Callback */
      HAL_DMA2D_LineEventCallback(hdma2d);
    }
  }  
  /* Transfer Complete Interrupt management ************************************/
  if(__HAL_DMA2D_GET_FLAG(hdma2d, DMA2D_FLAG_TC) != RESET)
  {
    if(__HAL_DMA2D_GET_IT_SOURCE(hdma2d, DMA2D_IT_TC) != RESET)
    { 
      /* Disable the transfer complete interrupt */
      __HAL_DMA2D_DISABLE_IT(hdma2d, DMA2D_IT_TC);
  
      /* Clear the transfer complete flag */  
      __HAL_DMA2D_CLEAR_FLAG(hdma2d, DMA2D_FLAG_TC);

      /* Update error code */
      hdma2d->ErrorCode |= HAL_DMA2D_ERROR_NONE;    
    
      /* Change DMA2D state */
      hdma2d->State = HAL_DMA2D_STATE_READY;
    
      /* Process Unlocked */
      __HAL_UNLOCK(hdma2d);       
      
      if(hdma2d->XferCpltCallback != NULL)
      {
        /* Transfer complete Callback */
        hdma2d->XferCpltCallback(hdma2d);
      }         
    }
  }
  /* CLUT Transfer Complete Interrupt management ******************************/
  if(__HAL_DMA2D_GET_FLAG(hdma2d, DMA2D_FLAG_CTC) != RESET)
  {
    if(__HAL_DMA2D_GET_IT_SOURCE(hdma2d, DMA2D_IT_CTC) != RESET)
    { 
      /* Disable the CLUT transfer complete interrupt */
      __HAL_DMA2D_DISABLE_IT(hdma2d, DMA2D_IT_CTC);
  
      /* Clear the CLUT transfer complete flag */  
      __HAL_DMA2D_CLEAR_FLAG(hdma2d, DMA2D_FLAG_CTC);

      /* Update error code */
      hdma2d->ErrorCode |= HAL_DMA2D_ERROR_NONE;    
    
      /* Change DMA2D state */
      hdma2d->State = HAL_DMA2D_STATE_READY;
    
      /* Process Unlocked */
      __HAL_UNLOCK(hdma2d);       
      
      /* CLUT Transfer complete Callback */
      HAL_DMA2D_CLUTLoadingCpltCallback(hdma2d);         
    }
  }  
  
}

/**
  * @brief  Transfer watermark callback.
  * @param  hdma2d: pointer to a DMA2D_HandleTypeDef structure that contains
  *                 the configuration information for the DMA2D.
  * @retval None
  */
__weak void HAL_DMA2D_LineEventCallback(DMA2D_HandleTypeDef *hdma2d)
{
  /* Prevent unused argument(s) compilation warning */
  UNUSED(hdma2d);
  
  /* NOTE : This function Should not be modified, when the callback is needed,
            the HAL_DMA2D_LineEventCallback could be implemented in the user file
   */
}

/**
  * @brief  CLUT Transfer Complete callback.
  * @param  hdma2d: pointer to a DMA2D_HandleTypeDef structure that contains
  *                 the configuration information for the DMA2D.
  * @retval None
  */
__weak void HAL_DMA2D_CLUTLoadingCpltCallback(DMA2D_HandleTypeDef *hdma2d)
{
  /* Prevent unused argument(s) compilation warning */
  UNUSED(hdma2d);
  
  /* NOTE : This function Should not be modified, when the callback is needed,
            the HAL_DMA2D_CLUTLoadingCpltCallback could be implemented in the user file
   */
}

/**
  * @}
  */

/** @defgroup DMA2D_Group3 Peripheral Control functions
 *  @brief    Peripheral Control functions 
 *
@verbatim   
 ===============================================================================
                    ##### Peripheral Control functions #####
 ===============================================================================  
    [..]  This section provides functions allowing to:
      (+) Configure the DMA2D foreground or/and background parameters.
      (+) Configure the DMA2D CLUT transfer.
      (+) Enable DMA2D CLUT.
      (+) Disable DMA2D CLUT.
      (+) Configure the line watermark

@endverbatim
  * @{
  */
/**
  * @brief  Configure the DMA2D Layer according to the specified
  *         parameters in the DMA2D_InitTypeDef and create the associated handle.
  * @param  hdma2d: pointer to a DMA2D_HandleTypeDef structure that contains
  *                 the configuration information for the DMA2D.
  * @param  LayerIdx: DMA2D Layer index.
  *                   This parameter can be one of the following values:
  *                   0(background) / 1(foreground)
  * @retval HAL status
  */
HAL_StatusTypeDef HAL_DMA2D_ConfigLayer(DMA2D_HandleTypeDef *hdma2d, uint32_t LayerIdx)
{ 
  DMA2D_LayerCfgTypeDef *pLayerCfg = &hdma2d->LayerCfg[LayerIdx];
  
  uint32_t tmp = 0U;

  /* Check the parameters */
  assert_param(IS_DMA2D_LAYER(LayerIdx));  
  assert_param(IS_DMA2D_OFFSET(pLayerCfg->InputOffset));  
  
  if(hdma2d->Init.Mode != DMA2D_R2M)
  {  
    assert_param(IS_DMA2D_INPUT_COLOR_MODE(pLayerCfg->InputColorMode));
    if(hdma2d->Init.Mode != DMA2D_M2M)
    {
      assert_param(IS_DMA2D_ALPHA_MODE(pLayerCfg->AlphaMode));
    }
  }

  /* Process locked */
  __HAL_LOCK(hdma2d);
  
  /* Change DMA2D peripheral state */
  hdma2d->State = HAL_DMA2D_STATE_BUSY;  
  
  /* Configure the background DMA2D layer */
  if(LayerIdx == 0U)
  {
    /* DMA2D BGPFCR register configuration -----------------------------------*/
    /* Get the BGPFCCR register value */
    tmp = hdma2d->Instance->BGPFCCR;
    
    /* Clear Input color mode, alpha value and alpha mode bits */
    tmp &= (uint32_t)~(DMA2D_BGPFCCR_CM | DMA2D_BGPFCCR_AM | DMA2D_BGPFCCR_ALPHA); 
    
    if ((pLayerCfg->InputColorMode == CM_A4) || (pLayerCfg->InputColorMode == CM_A8))
    {
      /* Prepare the value to be wrote to the BGPFCCR register */
      tmp |= (pLayerCfg->InputColorMode | (pLayerCfg->AlphaMode << 16U) | ((pLayerCfg->InputAlpha) & 0xFF000000U));
    }
    else
    {
      /* Prepare the value to be wrote to the BGPFCCR register */
      tmp |= (pLayerCfg->InputColorMode | (pLayerCfg->AlphaMode << 16U) | (pLayerCfg->InputAlpha << 24U));
    }
    
    /* Write to DMA2D BGPFCCR register */
    hdma2d->Instance->BGPFCCR = tmp; 
    
    /* DMA2D BGOR register configuration -------------------------------------*/  
    /* Get the BGOR register value */
    tmp = hdma2d->Instance->BGOR;
    
    /* Clear colors bits */
    tmp &= (uint32_t)~DMA2D_BGOR_LO; 
    
    /* Prepare the value to be wrote to the BGOR register */
    tmp |= pLayerCfg->InputOffset;
    
    /* Write to DMA2D BGOR register */
    hdma2d->Instance->BGOR = tmp;
    
    if ((pLayerCfg->InputColorMode == CM_A4) || (pLayerCfg->InputColorMode == CM_A8))
    {
      /* Prepare the value to be wrote to the BGCOLR register */
      tmp = ((pLayerCfg->InputAlpha) & 0x00FFFFFFU);
    
      /* Write to DMA2D BGCOLR register */
      hdma2d->Instance->BGCOLR = tmp;
    }    
  }
  /* Configure the foreground DMA2D layer */
  else
  {
    /* DMA2D FGPFCR register configuration -----------------------------------*/
    /* Get the FGPFCCR register value */
    tmp = hdma2d->Instance->FGPFCCR;
    
    /* Clear Input color mode, alpha value and alpha mode bits */
    tmp &= (uint32_t)~(DMA2D_FGPFCCR_CM | DMA2D_FGPFCCR_AM | DMA2D_FGPFCCR_ALPHA); 
    
    if ((pLayerCfg->InputColorMode == CM_A4) || (pLayerCfg->InputColorMode == CM_A8))
    {
      /* Prepare the value to be wrote to the FGPFCCR register */
      tmp |= (pLayerCfg->InputColorMode | (pLayerCfg->AlphaMode << 16U) | ((pLayerCfg->InputAlpha) & 0xFF000000U));
    }
    else
    {
      /* Prepare the value to be wrote to the FGPFCCR register */
      tmp |= (pLayerCfg->InputColorMode | (pLayerCfg->AlphaMode << 16U) | (pLayerCfg->InputAlpha << 24U));
    }
    
    /* Write to DMA2D FGPFCCR register */
    hdma2d->Instance->FGPFCCR = tmp; 
    
    /* DMA2D FGOR register configuration -------------------------------------*/  
    /* Get the FGOR register value */
    tmp = hdma2d->Instance->FGOR;
    
    /* Clear colors bits */
    tmp &= (uint32_t)~DMA2D_FGOR_LO; 
    
    /* Prepare the value to be wrote to the FGOR register */
    tmp |= pLayerCfg->InputOffset;
    
    /* Write to DMA2D FGOR register */
    hdma2d->Instance->FGOR = tmp;
   
    if ((pLayerCfg->InputColorMode == CM_A4) || (pLayerCfg->InputColorMode == CM_A8))
    {
      /* Prepare the value to be wrote to the FGCOLR register */
      tmp = ((pLayerCfg->InputAlpha) & 0x00FFFFFFU);
    
      /* Write to DMA2D FGCOLR register */
      hdma2d->Instance->FGCOLR = tmp;
    }   
  }    
  /* Initialize the DMA2D state*/
  hdma2d->State  = HAL_DMA2D_STATE_READY;
  
  /* Process unlocked */
  __HAL_UNLOCK(hdma2d);  
  
  return HAL_OK;
}

/**
  * @brief  Configure the DMA2D CLUT Transfer.
  * @param  hdma2d:   pointer to a DMA2D_HandleTypeDef structure that contains
  *                   the configuration information for the DMA2D.
  * @param  CLUTCfg:  pointer to a DMA2D_CLUTCfgTypeDef structure that contains
  *                   the configuration information for the color look up table.
  * @param  LayerIdx: DMA2D Layer index.
  *                   This parameter can be one of the following values:
  *                   0(background) / 1(foreground)
  * @retval HAL status
  */
HAL_StatusTypeDef HAL_DMA2D_ConfigCLUT(DMA2D_HandleTypeDef *hdma2d, DMA2D_CLUTCfgTypeDef CLUTCfg, uint32_t LayerIdx)
{
  /* Check the parameters */
  assert_param(IS_DMA2D_LAYER(LayerIdx));   
  assert_param(IS_DMA2D_CLUT_CM(CLUTCfg.CLUTColorMode));
  assert_param(IS_DMA2D_CLUT_SIZE(CLUTCfg.Size));
  
  /* Process locked */
  __HAL_LOCK(hdma2d);
  
  /* Change DMA2D peripheral state */
  hdma2d->State = HAL_DMA2D_STATE_BUSY; 
  
  /* Configure the CLUT of the background DMA2D layer */
  if(LayerIdx == 0U)
  {
    /* Write to DMA2D BGCMAR register */
    hdma2d->Instance->BGCMAR = (uint32_t)CLUTCfg.pCLUT;
    
   /* Write background CLUT size and CLUT color mode */
    MODIFY_REG(hdma2d->Instance->BGPFCCR, (DMA2D_BGPFCCR_CS | DMA2D_BGPFCCR_CCM), ((CLUTCfg.Size << 8U) | (CLUTCfg.CLUTColorMode << 4U)));     
  }
  /* Configure the CLUT of the foreground DMA2D layer */
  else
  {
    /* Write to DMA2D FGCMAR register */
    hdma2d->Instance->FGCMAR = (uint32_t)CLUTCfg.pCLUT;
    
    /* Write foreground CLUT size and CLUT color mode */
    MODIFY_REG(hdma2d->Instance->FGPFCCR, (DMA2D_FGPFCCR_CS | DMA2D_FGPFCCR_CCM), ((CLUTCfg.Size << 8U) | (CLUTCfg.CLUTColorMode << 4U)));  
  }

  /* Set the DMA2D state to Ready*/
  hdma2d->State  = HAL_DMA2D_STATE_READY;
  
  /* Process unlocked */
  __HAL_UNLOCK(hdma2d); 
  
  return HAL_OK;
}

/**
  * @brief  Enable the DMA2D CLUT Transfer.
  * @param  hdma2d:   pointer to a DMA2D_HandleTypeDef structure that contains
  *                   the configuration information for the DMA2D.
  * @param  LayerIdx: DMA2D Layer index.
  *                   This parameter can be one of the following values:
  *                   0(background) / 1(foreground)
  * @retval HAL status
  */
HAL_StatusTypeDef HAL_DMA2D_EnableCLUT(DMA2D_HandleTypeDef *hdma2d, uint32_t LayerIdx)
{
  /* Check the parameters */
  assert_param(IS_DMA2D_LAYER(LayerIdx));
  
  /* Process locked */
  __HAL_LOCK(hdma2d);
  
  /* Change DMA2D peripheral state */
  hdma2d->State = HAL_DMA2D_STATE_BUSY;
  
  if(LayerIdx == 0U)
  {
    /* Enable the CLUT loading for the background */
    hdma2d->Instance->BGPFCCR |= DMA2D_BGPFCCR_START;
  }
  else
  {
    /* Enable the CLUT loading for the foreground */
    hdma2d->Instance->FGPFCCR |= DMA2D_FGPFCCR_START;
  }
  
  return HAL_OK;
}

/**
  * @brief  Disable the DMA2D CLUT Transfer.
  * @param  hdma2d:   pointer to a DMA2D_HandleTypeDef structure that contains
  *                   the configuration information for the DMA2D.
  * @param  LayerIdx: DMA2D Layer index.
  *                   This parameter can be one of the following values:
  *                   0(background) / 1(foreground)
  * @retval HAL status
  */
HAL_StatusTypeDef HAL_DMA2D_DisableCLUT(DMA2D_HandleTypeDef *hdma2d, uint32_t LayerIdx)
{
  /* Check the parameters */
  assert_param(IS_DMA2D_LAYER(LayerIdx));
  
  if(LayerIdx == 0U)
  {
    /* Disable the CLUT loading for the background */
    hdma2d->Instance->BGPFCCR &= ~DMA2D_BGPFCCR_START;
  }
  else
  {
    /* Disable the CLUT loading for the foreground */
    hdma2d->Instance->FGPFCCR &= ~DMA2D_FGPFCCR_START;
  } 
  
  return HAL_OK;
}

/**
  * @brief  Define the configuration of the line watermark .
  * @param  hdma2d: pointer to a DMA2D_HandleTypeDef structure that contains
  *                 the configuration information for the DMA2D.
  * @param  Line:   Line Watermark configuration.
  * @retval HAL status
  */

HAL_StatusTypeDef HAL_DMA2D_ProgramLineEvent(DMA2D_HandleTypeDef *hdma2d, uint32_t Line)
{
  /* Check the parameters */
  assert_param(IS_DMA2D_LineWatermark(Line));
  
  /* Process locked */
  __HAL_LOCK(hdma2d);
  
  /* Change DMA2D peripheral state */
  hdma2d->State = HAL_DMA2D_STATE_BUSY;

  /* Sets the Line watermark configuration */
  DMA2D->LWR = (uint32_t)Line;
  
  /* Initialize the DMA2D state*/
  hdma2d->State = HAL_DMA2D_STATE_READY;
  
  /* Process unlocked */
  __HAL_UNLOCK(hdma2d);  
  
  return HAL_OK;  
}

/**
  * @}
  */

/** @defgroup DMA2D_Group4 Peripheral State functions
 *  @brief    Peripheral State functions 
 *
@verbatim   
 ===============================================================================
                  ##### Peripheral State and Errors functions #####
 ===============================================================================  
    [..]
    This subsection provides functions allowing to :
      (+) Check the DMA2D state
      (+) Get error code  

@endverbatim
  * @{
  */ 

/**
  * @brief  Return the DMA2D state
  * @param  hdma2d: pointer to a DMA2D_HandleTypeDef structure that contains
  *                 the configuration information for the DMA2D.  
  * @retval HAL state
  */
HAL_DMA2D_StateTypeDef HAL_DMA2D_GetState(DMA2D_HandleTypeDef *hdma2d)
{  
  return hdma2d->State;
}

/**
  * @brief  Return the DMA2D error code
  * @param  hdma2d : pointer to a DMA2D_HandleTypeDef structure that contains
  *               the configuration information for DMA2D.
  * @retval DMA2D Error Code
  */
uint32_t HAL_DMA2D_GetError(DMA2D_HandleTypeDef *hdma2d)
{
  return hdma2d->ErrorCode;
}

/**
  * @}
  */


/**
  * @brief  Set the DMA2D Transfer parameter.
  * @param  hdma2d:     pointer to a DMA2D_HandleTypeDef structure that contains
  *                     the configuration information for the specified DMA2D.  
  * @param  pdata:      The source memory Buffer address
  * @param  DstAddress: The destination memory Buffer address
  * @param  Width:      The width of data to be transferred from source to destination.
  * @param  Height:     The height of data to be transferred from source to destination.
  * @retval HAL status
  */
static void DMA2D_SetConfig(DMA2D_HandleTypeDef *hdma2d, uint32_t pdata, uint32_t DstAddress, uint32_t Width, uint32_t Height)
{  
  uint32_t tmp = 0U;
  uint32_t tmp1 = 0U;
  uint32_t tmp2 = 0U;
  uint32_t tmp3 = 0U;
  uint32_t tmp4 = 0U;
  
  tmp = Width << 16U;
  
  /* Configure DMA2D data size */
  hdma2d->Instance->NLR = (Height | tmp);
  
  /* Configure DMA2D destination address */
  hdma2d->Instance->OMAR = DstAddress;
 
  /* Register to memory DMA2D mode selected */
  if (hdma2d->Init.Mode == DMA2D_R2M)
  {    
    tmp1 = pdata & DMA2D_OCOLR_ALPHA_1;
    tmp2 = pdata & DMA2D_OCOLR_RED_1;
    tmp3 = pdata & DMA2D_OCOLR_GREEN_1;
    tmp4 = pdata & DMA2D_OCOLR_BLUE_1;
    
    /* Prepare the value to be wrote to the OCOLR register according to the color mode */
    if (hdma2d->Init.ColorMode == DMA2D_ARGB8888)
    {
      tmp = (tmp3 | tmp2 | tmp1| tmp4);
    }
    else if (hdma2d->Init.ColorMode == DMA2D_RGB888)
    {
      tmp = (tmp3 | tmp2 | tmp4);  
    }
    else if (hdma2d->Init.ColorMode == DMA2D_RGB565)
    {
      tmp2 = (tmp2 >> 19U);
      tmp3 = (tmp3 >> 10U);
      tmp4 = (tmp4 >> 3U);
      tmp  = ((tmp3 << 5U) | (tmp2 << 11U) | tmp4); 
    }
    else if (hdma2d->Init.ColorMode == DMA2D_ARGB1555)
    { 
      tmp1 = (tmp1 >> 31U);
      tmp2 = (tmp2 >> 19U);
      tmp3 = (tmp3 >> 11U);
      tmp4 = (tmp4 >> 3U);      
      tmp  = ((tmp3 << 5U) | (tmp2 << 10U) | (tmp1 << 15U) | tmp4);    
    } 
    else /* DMA2D_CMode = DMA2D_ARGB4444 */
    {
      tmp1 = (tmp1 >> 28U);
      tmp2 = (tmp2 >> 20U);
      tmp3 = (tmp3 >> 12U);
      tmp4 = (tmp4 >> 4U);
      tmp  = ((tmp3 << 4U) | (tmp2 << 8U) | (tmp1 << 12U) | tmp4);
    }    
    /* Write to DMA2D OCOLR register */
    hdma2d->Instance->OCOLR = tmp;
  } 
  else /* M2M, M2M_PFC or M2M_Blending DMA2D Mode */
  {
    /* Configure DMA2D source address */
    hdma2d->Instance->FGMAR = pdata;
  }
}

/**
  * @}
  */
#endif /* STM32F427xx || STM32F437xx || STM32F429xx || STM32F439xx || STM32F469xx || STM32F479xx */
#endif /* HAL_DMA2D_MODULE_ENABLED */
/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
