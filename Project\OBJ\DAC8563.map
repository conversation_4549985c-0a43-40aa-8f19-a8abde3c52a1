Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to stm32f429_winner.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to led.o(i.LED_Init) for LED_Init
    main.o(i.main) refers to dac8563.o(i.DAC8563_Init) for DAC8563_Init
    main.o(i.main) refers to dac8563.o(i.DAC8563_Write_CHA_CHB) for DAC8563_Write_CHA_CHB
    main.o(i.main) refers to main.o(.data) for sin_data
    stm32f4xx_it.o(i.SysTick_Handler) refers to stm32f4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f429_winner.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    stm32f429_winner.o(i.SystemClock_Config) refers to stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableOverDrive) for HAL_PWREx_EnableOverDrive
    stm32f429_winner.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    delay.o(i.SysTick_clkconfig) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig) for HAL_SYSTICK_CLKSourceConfig
    delay.o(i.SysTick_clkconfig) refers to delay.o(.data) for fac_us
    delay.o(i.SysTick_delay_ms) refers to delay.o(i.SysTick_delay_us) for SysTick_delay_us
    delay.o(i.SysTick_delay_us) refers to delay.o(.data) for fac_us
    delay.o(i.delay_ms) refers to delay.o(i.delay_us) for delay_us
    led.o(i.BSP_LED_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    led.o(i.BSP_LED_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    led.o(i.BSP_LED_Init) refers to led.o(.constdata) for GPIO_PIN
    led.o(i.BSP_LED_Init) refers to led.o(.data) for GPIO_PORT
    led.o(i.LED_Init) refers to led.o(i.BSP_LED_Init) for BSP_LED_Init
    lcd.o(i.HAL_SRAM_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    lcd.o(i.LCD_Clear) refers to ltdc.o(i.LTDC_Clear) for LTDC_Clear
    lcd.o(i.LCD_Clear) refers to lcd.o(i.LCD_Fill) for LCD_Fill
    lcd.o(i.LCD_Clear) refers to ltdc.o(.bss) for lcdltdc
    lcd.o(i.LCD_Clear) refers to lcd.o(.bss) for lcddev
    lcd.o(i.LCD_Color_Fill) refers to ltdc.o(i.LTDC_Color_Fill) for LTDC_Color_Fill
    lcd.o(i.LCD_Color_Fill) refers to lcd.o(i.LCD_Set_Window) for LCD_Set_Window
    lcd.o(i.LCD_Color_Fill) refers to lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lcd.o(i.LCD_Color_Fill) refers to lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    lcd.o(i.LCD_Color_Fill) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_Color_Fill) refers to ltdc.o(.bss) for lcdltdc
    lcd.o(i.LCD_Color_Fill) refers to lcd.o(.bss) for lcddev
    lcd.o(i.LCD_DisplayOff) refers to ltdc.o(i.LTDC_Switch) for LTDC_Switch
    lcd.o(i.LCD_DisplayOff) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_DisplayOff) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.LCD_DisplayOff) refers to ltdc.o(.bss) for lcdltdc
    lcd.o(i.LCD_DisplayOff) refers to lcd.o(.bss) for lcddev
    lcd.o(i.LCD_DisplayOn) refers to ltdc.o(i.LTDC_Switch) for LTDC_Switch
    lcd.o(i.LCD_DisplayOn) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_DisplayOn) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.LCD_DisplayOn) refers to ltdc.o(.bss) for lcdltdc
    lcd.o(i.LCD_DisplayOn) refers to lcd.o(.bss) for lcddev
    lcd.o(i.LCD_Display_Dir) refers to lcd.o(.bss) for lcddev
    lcd.o(i.LCD_DrawLine) refers to lcd.o(i.LCD_DrawPoint) for LCD_DrawPoint
    lcd.o(i.LCD_DrawPoint) refers to ltdc.o(i.LTDC_Draw_Point) for LTDC_Draw_Point
    lcd.o(i.LCD_DrawPoint) refers to lcd.o(i.LCD_Set_Window) for LCD_Set_Window
    lcd.o(i.LCD_DrawPoint) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_DrawPoint) refers to lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lcd.o(i.LCD_DrawPoint) refers to ltdc.o(.bss) for lcdltdc
    lcd.o(i.LCD_DrawPoint) refers to lcd.o(.data) for POINT_COLOR
    lcd.o(i.LCD_DrawPoint) refers to lcd.o(.bss) for lcddev
    lcd.o(i.LCD_DrawRectangle) refers to lcd.o(i.LCD_DrawLine) for LCD_DrawLine
    lcd.o(i.LCD_Draw_Circle) refers to lcd.o(i.LCD_DrawPoint) for LCD_DrawPoint
    lcd.o(i.LCD_Fast_DrawPoint) refers to ltdc.o(i.LTDC_Draw_Point) for LTDC_Draw_Point
    lcd.o(i.LCD_Fast_DrawPoint) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_Fast_DrawPoint) refers to lcd.o(i.LCD_WR_DATA) for LCD_WR_DATA
    lcd.o(i.LCD_Fast_DrawPoint) refers to lcd.o(i.LCD_Set_Window) for LCD_Set_Window
    lcd.o(i.LCD_Fast_DrawPoint) refers to ltdc.o(.bss) for lcdltdc
    lcd.o(i.LCD_Fast_DrawPoint) refers to lcd.o(.bss) for lcddev
    lcd.o(i.LCD_Fill) refers to ltdc.o(i.LTDC_Fill) for LTDC_Fill
    lcd.o(i.LCD_Fill) refers to lcd.o(i.LCD_Set_Window) for LCD_Set_Window
    lcd.o(i.LCD_Fill) refers to lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lcd.o(i.LCD_Fill) refers to lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    lcd.o(i.LCD_Fill) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_Fill) refers to ltdc.o(.bss) for lcdltdc
    lcd.o(i.LCD_Fill) refers to lcd.o(.bss) for lcddev
    lcd.o(i.LCD_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    lcd.o(i.LCD_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    lcd.o(i.LCD_Init) refers to ltdc.o(i.LTDC_Init) for LTDC_Init
    lcd.o(i.LCD_Init) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) for HAL_SRAM_Init
    lcd.o(i.LCD_Init) refers to delay.o(i.delay_ms) for delay_ms
    lcd.o(i.LCD_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_ReadReg) for LCD_ReadReg
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_RD_DATA) for LCD_RD_DATA
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_WR_DATA) for LCD_WR_DATA
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_Display_Dir) for LCD_Display_Dir
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_Set_Window) for LCD_Set_Window
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.LCD_Init) refers to lcd.o(.bss) for lcddev
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_Clear) for LCD_Clear
    lcd.o(i.LCD_ReadPoint) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_ReadPoint) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_ReadPoint) refers to lcd.o(i.LCD_RD_DATA) for LCD_RD_DATA
    lcd.o(i.LCD_ReadPoint) refers to lcd.o(i.opt_delay) for opt_delay
    lcd.o(i.LCD_ReadPoint) refers to lcd.o(.bss) for lcddev
    lcd.o(i.LCD_ReadReg) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_ReadReg) refers to delay.o(i.delay_us) for delay_us
    lcd.o(i.LCD_ReadReg) refers to lcd.o(i.LCD_RD_DATA) for LCD_RD_DATA
    lcd.o(i.LCD_SetCursor) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_SetCursor) refers to lcd.o(i.LCD_WR_DATA) for LCD_WR_DATA
    lcd.o(i.LCD_SetCursor) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.LCD_SetCursor) refers to lcd.o(.bss) for lcddev
    lcd.o(i.LCD_Set_Window) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_Set_Window) refers to lcd.o(i.LCD_WR_DATA) for LCD_WR_DATA
    lcd.o(i.LCD_Set_Window) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.LCD_Set_Window) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_Set_Window) refers to lcd.o(.bss) for lcddev
    lcd.o(i.LCD_ShowChar) refers to lcd.o(i.LCD_Fast_DrawPoint) for LCD_Fast_DrawPoint
    lcd.o(i.LCD_ShowChar) refers to lcd.o(.constdata) for asc2_1206
    lcd.o(i.LCD_ShowChar) refers to lcd.o(.data) for POINT_COLOR
    lcd.o(i.LCD_ShowChar) refers to lcd.o(.bss) for lcddev
    lcd.o(i.LCD_ShowNum) refers to lcd.o(i.LCD_Pow) for LCD_Pow
    lcd.o(i.LCD_ShowNum) refers to lcd.o(i.LCD_ShowChar) for LCD_ShowChar
    lcd.o(i.LCD_ShowString) refers to lcd.o(i.LCD_ShowChar) for LCD_ShowChar
    lcd.o(i.LCD_ShowString) refers to lcd.o(.bss) for lcddev
    lcd.o(i.LCD_ShowxNum) refers to lcd.o(i.LCD_Pow) for LCD_Pow
    lcd.o(i.LCD_ShowxNum) refers to lcd.o(i.LCD_ShowChar) for LCD_ShowChar
    lcd.o(i.LCD_WriteRAM_Prepare) refers to lcd.o(.bss) for lcddev
    lcd.o(i.gui_draw_hline) refers to lcd.o(i.LCD_Fill) for LCD_Fill
    lcd.o(i.gui_fill_circle) refers to lcd.o(i.gui_draw_hline) for gui_draw_hline
    lcd.o(i.lcd_draw_bline) refers to lcd.o(i.gui_fill_circle) for gui_fill_circle
    ltdc.o(i.HAL_LTDC_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    ltdc.o(i.LCD_GPIO_Config) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    ltdc.o(i.LCD_GPIO_Config) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ltdc.o(i.LCD_WriteByteSPI) refers to delay.o(i.delay_us) for delay_us
    ltdc.o(i.LTDC_Clear) refers to ltdc.o(i.LTDC_Fill) for LTDC_Fill
    ltdc.o(i.LTDC_Clear) refers to ltdc.o(.bss) for lcdltdc
    ltdc.o(i.LTDC_Clk_Set) refers to stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    ltdc.o(i.LTDC_Color_Fill) refers to ltdc.o(i.LTDC_Draw_Point) for LTDC_Draw_Point
    ltdc.o(i.LTDC_Display_Dir) refers to ltdc.o(.bss) for lcdltdc
    ltdc.o(i.LTDC_Display_Dir) refers to lcd.o(.bss) for lcddev
    ltdc.o(i.LTDC_Draw_Point) refers to ltdc.o(.bss) for lcdltdc
    ltdc.o(i.LTDC_Draw_Point) refers to lcd.o(.bss) for lcddev
    ltdc.o(i.LTDC_Draw_Point) refers to ltdc.o(.data) for ltdc_framebuf
    ltdc.o(i.LTDC_Fill) refers to ltdc.o(.bss) for lcdltdc
    ltdc.o(i.LTDC_Fill) refers to lcd.o(.bss) for lcddev
    ltdc.o(i.LTDC_Fill) refers to ltdc.o(.data) for ltdc_framebuf
    ltdc.o(i.LTDC_Init) refers to ltdc.o(i.Lcd_Initialize) for Lcd_Initialize
    ltdc.o(i.LTDC_Init) refers to ltdc.o(i.LTDC_Clk_Set) for LTDC_Clk_Set
    ltdc.o(i.LTDC_Init) refers to stm32f4xx_hal_ltdc.o(i.HAL_LTDC_Init) for HAL_LTDC_Init
    ltdc.o(i.LTDC_Init) refers to ltdc.o(i.LTDC_Layer_Parameter_Config) for LTDC_Layer_Parameter_Config
    ltdc.o(i.LTDC_Init) refers to ltdc.o(i.LTDC_Layer_Window_Config) for LTDC_Layer_Window_Config
    ltdc.o(i.LTDC_Init) refers to ltdc.o(i.LTDC_Display_Dir) for LTDC_Display_Dir
    ltdc.o(i.LTDC_Init) refers to ltdc.o(i.LTDC_Select_Layer) for LTDC_Select_Layer
    ltdc.o(i.LTDC_Init) refers to ltdc.o(i.LTDC_Clear) for LTDC_Clear
    ltdc.o(i.LTDC_Init) refers to lcd.o(.bss) for lcddev
    ltdc.o(i.LTDC_Init) refers to ltdc.o(.bss) for lcdltdc
    ltdc.o(i.LTDC_Init) refers to ltdc.o(.ARM.__AT_0xC0000000) for ltdc_lcd_framebuf
    ltdc.o(i.LTDC_Init) refers to ltdc.o(.data) for ltdc_framebuf
    ltdc.o(i.LTDC_Layer_Parameter_Config) refers to stm32f4xx_hal_ltdc.o(i.HAL_LTDC_ConfigLayer) for HAL_LTDC_ConfigLayer
    ltdc.o(i.LTDC_Layer_Parameter_Config) refers to ltdc.o(.bss) for lcdltdc
    ltdc.o(i.LTDC_Layer_Switch) refers to ltdc.o(.bss) for LTDC_Handler
    ltdc.o(i.LTDC_Layer_Window_Config) refers to stm32f4xx_hal_ltdc.o(i.HAL_LTDC_SetWindowPosition) for HAL_LTDC_SetWindowPosition
    ltdc.o(i.LTDC_Layer_Window_Config) refers to stm32f4xx_hal_ltdc.o(i.HAL_LTDC_SetWindowSize) for HAL_LTDC_SetWindowSize
    ltdc.o(i.LTDC_Layer_Window_Config) refers to ltdc.o(.bss) for LTDC_Handler
    ltdc.o(i.LTDC_Read_Point) refers to ltdc.o(.bss) for lcdltdc
    ltdc.o(i.LTDC_Read_Point) refers to lcd.o(.bss) for lcddev
    ltdc.o(i.LTDC_Read_Point) refers to ltdc.o(.data) for ltdc_framebuf
    ltdc.o(i.LTDC_Select_Layer) refers to ltdc.o(.bss) for lcdltdc
    ltdc.o(i.LTDC_Switch) refers to ltdc.o(.bss) for LTDC_Handler
    ltdc.o(i.Lcd_Initialize) refers to ltdc.o(i.LCD_GPIO_Config) for LCD_GPIO_Config
    ltdc.o(i.Lcd_Initialize) refers to delay.o(i.delay_ms) for delay_ms
    ltdc.o(i.Lcd_Initialize) refers to ltdc.o(i.SPI_WriteComm) for SPI_WriteComm
    ltdc.o(i.Lcd_Initialize) refers to ltdc.o(i.SPI_WriteData) for SPI_WriteData
    ltdc.o(i.SPI_WriteComm) refers to delay.o(i.delay_us) for delay_us
    ltdc.o(i.SPI_WriteComm) refers to ltdc.o(i.LCD_WriteByteSPI) for LCD_WriteByteSPI
    ltdc.o(i.SPI_WriteData) refers to delay.o(i.delay_us) for delay_us
    ltdc.o(i.SPI_WriteData) refers to ltdc.o(i.LCD_WriteByteSPI) for LCD_WriteByteSPI
    sdram.o(i.HAL_SDRAM_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    sdram.o(i.SDRAM_Init) refers to stm32f4xx_hal_sdram.o(i.HAL_SDRAM_Init) for HAL_SDRAM_Init
    sdram.o(i.SDRAM_Init) refers to sdram.o(i.SDRAM_Initialization_Sequence) for SDRAM_Initialization_Sequence
    sdram.o(i.SDRAM_Init) refers to stm32f4xx_hal_sdram.o(i.HAL_SDRAM_ProgramRefreshRate) for HAL_SDRAM_ProgramRefreshRate
    sdram.o(i.SDRAM_Init) refers to sdram.o(.bss) for SDRAM_Handler
    sdram.o(i.SDRAM_Initialization_Sequence) refers to sdram.o(i.SDRAM_Send_Cmd) for SDRAM_Send_Cmd
    sdram.o(i.SDRAM_Initialization_Sequence) refers to delay.o(i.delay_us) for delay_us
    sdram.o(i.SDRAM_Send_Cmd) refers to stm32f4xx_hal_sdram.o(i.HAL_SDRAM_SendCommand) for HAL_SDRAM_SendCommand
    sdram.o(i.SDRAM_Send_Cmd) refers to sdram.o(.bss) for SDRAM_Handler
    usart.o(.rev16_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.revsh_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.rrx_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.HAL_UART_MspInit) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_RxCpltCallback) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.HAL_UART_RxCpltCallback) refers to usart.o(.data) for USART_RX_STA
    usart.o(i.HAL_UART_RxCpltCallback) refers to usart.o(.bss) for USART_RX_BUF
    usart.o(i.USART1_IRQHandler) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    usart.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_GetState) for HAL_UART_GetState
    usart.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    usart.o(i.USART1_IRQHandler) refers to usart.o(.bss) for UART_Handler
    usart.o(i.USART1_IRQHandler) refers to usart.o(.data) for aRxBuffer
    usart.o(i._sys_exit) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.fputc) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.uart_init) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.uart_init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.uart_init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    usart.o(i.uart_init) refers to usart.o(.bss) for UART_Handler
    usart.o(i.uart_init) refers to usart.o(.data) for aRxBuffer
    usart.o(.bss) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    spi.o(i.HAL_SPI_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    spi.o(i.SPI5_Init) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_Init) for HAL_SPI_Init
    spi.o(i.SPI5_Init) refers to spi.o(.bss) for SPI5_Handler
    spi.o(i.SPI5_Send_Byte) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) for HAL_SPI_TransmitReceive
    spi.o(i.SPI5_Send_Byte) refers to spi.o(.bss) for SPI5_Handler
    spi.o(i.SPI5_SetSpeed) refers to spi.o(.bss) for SPI5_Handler
    key.o(i.BSP_KEY_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    key.o(i.BSP_KEY_Init) refers to key.o(.constdata) for BUTTON_PIN
    key.o(i.BSP_KEY_Init) refers to key.o(.data) for BUTTON_PORT
    key.o(i.KEY_Init) refers to key.o(i.BSP_KEY_Init) for BSP_KEY_Init
    key.o(i.KEY_Scan) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key.o(i.KEY_Scan) refers to delay.o(i.delay_ms) for delay_ms
    key.o(i.KEY_Scan) refers to key.o(.data) for key_up
    dac8563.o(i.DAC8563_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    dac8563.o(i.DAC8563_Init) refers to spi.o(i.SPI5_Init) for SPI5_Init
    dac8563.o(i.DAC8563_Init) refers to dac8563.o(i.DAC8563_Write_Reg) for DAC8563_Write_Reg
    dac8563.o(i.DAC8563_Write_CHA) refers to dac8563.o(i.DAC8563_Write_Reg) for DAC8563_Write_Reg
    dac8563.o(i.DAC8563_Write_CHA_CHB) refers to dac8563.o(i.DAC8563_Write_Reg) for DAC8563_Write_Reg
    dac8563.o(i.DAC8563_Write_CHB) refers to dac8563.o(i.DAC8563_Write_Reg) for DAC8563_Write_Reg
    dac8563.o(i.DAC8563_Write_Reg) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    dac8563.o(i.DAC8563_Write_Reg) refers to spi.o(i.SPI5_Send_Byte) for SPI5_Send_Byte
    stm32f4xx_hal.o(i.HAL_DeInit) refers to stm32f4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(i.HAL_GetTick) refers to stm32f4xx_hal.o(.data) for uwTick
    stm32f4xx_hal.o(i.HAL_IncTick) refers to stm32f4xx_hal.o(.data) for uwTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_timebase_tim_template.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal.o(i.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping) refers to stm32f4xx_hal_cortex.o(i.NVIC_GetPriorityGrouping) for NVIC_GetPriorityGrouping
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(i.NVIC_GetPriorityGrouping) for NVIC_GetPriorityGrouping
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(i.NVIC_SetPriority) for NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(i.NVIC_SetPriority) for NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableOverDrive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableOverDrive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterUnderDriveSTOPMode) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_timebase_tim_template.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to stm32f4xx_hal_rcc.o(.constdata) for APBAHBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to stm32f4xx_hal_rcc.o(.constdata) for APBAHBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to stm32f4xx_hal_rcc.o(.constdata) for APBAHBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BootConfig) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisablePCROP) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnablePCROP) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnablePCROP) for FLASH_OB_EnablePCROP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisablePCROP) for FLASH_OB_DisablePCROP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BootConfig) for FLASH_OB_BootConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetWRP) for FLASH_OB_GetWRP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetUser) for FLASH_OB_GetUser
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetBOR) for FLASH_OB_GetBOR
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) for FLASH_OB_EnableWRP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) for FLASH_OB_DisableWRP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) for FLASH_OB_UserConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BOR_LevelConfig) for FLASH_OB_BOR_LevelConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_ltdc.o(i.HAL_LTDC_ConfigLayer) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    stm32f4xx_hal_ltdc.o(i.HAL_LTDC_ConfigLayer) refers to stm32f4xx_hal_ltdc.o(i.LTDC_SetConfig) for LTDC_SetConfig
    stm32f4xx_hal_ltdc.o(i.HAL_LTDC_DeInit) refers to stm32f4xx_hal_ltdc.o(i.HAL_LTDC_MspDeInit) for HAL_LTDC_MspDeInit
    stm32f4xx_hal_ltdc.o(i.HAL_LTDC_IRQHandler) refers to stm32f4xx_hal_ltdc.o(i.HAL_LTDC_ErrorCallback) for HAL_LTDC_ErrorCallback
    stm32f4xx_hal_ltdc.o(i.HAL_LTDC_IRQHandler) refers to stm32f4xx_hal_ltdc.o(i.HAL_LTDC_LineEventCallback) for HAL_LTDC_LineEventCallback
    stm32f4xx_hal_ltdc.o(i.HAL_LTDC_Init) refers to ltdc.o(i.HAL_LTDC_MspInit) for HAL_LTDC_MspInit
    stm32f4xx_hal_ltdc.o(i.HAL_LTDC_SetAddress) refers to stm32f4xx_hal_ltdc.o(i.LTDC_SetConfig) for LTDC_SetConfig
    stm32f4xx_hal_ltdc.o(i.HAL_LTDC_SetAlpha) refers to stm32f4xx_hal_ltdc.o(i.LTDC_SetConfig) for LTDC_SetConfig
    stm32f4xx_hal_ltdc.o(i.HAL_LTDC_SetPixelFormat) refers to stm32f4xx_hal_ltdc.o(i.LTDC_SetConfig) for LTDC_SetConfig
    stm32f4xx_hal_ltdc.o(i.HAL_LTDC_SetWindowPosition) refers to stm32f4xx_hal_ltdc.o(i.LTDC_SetConfig) for LTDC_SetConfig
    stm32f4xx_hal_ltdc.o(i.HAL_LTDC_SetWindowSize) refers to stm32f4xx_hal_ltdc.o(i.LTDC_SetConfig) for LTDC_SetConfig
    stm32f4xx_hal_sdram.o(i.HAL_SDRAM_DeInit) refers to stm32f4xx_hal_sdram.o(i.HAL_SDRAM_MspDeInit) for HAL_SDRAM_MspDeInit
    stm32f4xx_hal_sdram.o(i.HAL_SDRAM_DeInit) refers to stm32f4xx_ll_fmc.o(i.FMC_SDRAM_DeInit) for FMC_SDRAM_DeInit
    stm32f4xx_hal_sdram.o(i.HAL_SDRAM_GetModeStatus) refers to stm32f4xx_ll_fmc.o(i.FMC_SDRAM_GetModeStatus) for FMC_SDRAM_GetModeStatus
    stm32f4xx_hal_sdram.o(i.HAL_SDRAM_IRQHandler) refers to stm32f4xx_hal_sdram.o(i.HAL_SDRAM_RefreshErrorCallback) for HAL_SDRAM_RefreshErrorCallback
    stm32f4xx_hal_sdram.o(i.HAL_SDRAM_Init) refers to sdram.o(i.HAL_SDRAM_MspInit) for HAL_SDRAM_MspInit
    stm32f4xx_hal_sdram.o(i.HAL_SDRAM_Init) refers to stm32f4xx_ll_fmc.o(i.FMC_SDRAM_Init) for FMC_SDRAM_Init
    stm32f4xx_hal_sdram.o(i.HAL_SDRAM_Init) refers to stm32f4xx_ll_fmc.o(i.FMC_SDRAM_Timing_Init) for FMC_SDRAM_Timing_Init
    stm32f4xx_hal_sdram.o(i.HAL_SDRAM_ProgramRefreshRate) refers to stm32f4xx_ll_fmc.o(i.FMC_SDRAM_ProgramRefreshRate) for FMC_SDRAM_ProgramRefreshRate
    stm32f4xx_hal_sdram.o(i.HAL_SDRAM_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_sdram.o(i.HAL_SDRAM_Read_DMA) refers to stm32f4xx_hal_sdram.o(i.HAL_SDRAM_DMA_XferCpltCallback) for HAL_SDRAM_DMA_XferCpltCallback
    stm32f4xx_hal_sdram.o(i.HAL_SDRAM_Read_DMA) refers to stm32f4xx_hal_sdram.o(i.HAL_SDRAM_DMA_XferErrorCallback) for HAL_SDRAM_DMA_XferErrorCallback
    stm32f4xx_hal_sdram.o(i.HAL_SDRAM_SendCommand) refers to stm32f4xx_ll_fmc.o(i.FMC_SDRAM_SendCommand) for FMC_SDRAM_SendCommand
    stm32f4xx_hal_sdram.o(i.HAL_SDRAM_SetAutoRefreshNumber) refers to stm32f4xx_ll_fmc.o(i.FMC_SDRAM_SetAutoRefreshNumber) for FMC_SDRAM_SetAutoRefreshNumber
    stm32f4xx_hal_sdram.o(i.HAL_SDRAM_WriteProtection_Disable) refers to stm32f4xx_ll_fmc.o(i.FMC_SDRAM_WriteProtection_Disable) for FMC_SDRAM_WriteProtection_Disable
    stm32f4xx_hal_sdram.o(i.HAL_SDRAM_WriteProtection_Enable) refers to stm32f4xx_ll_fmc.o(i.FMC_SDRAM_WriteProtection_Enable) for FMC_SDRAM_WriteProtection_Enable
    stm32f4xx_hal_sdram.o(i.HAL_SDRAM_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_sdram.o(i.HAL_SDRAM_Write_DMA) refers to stm32f4xx_hal_sdram.o(i.HAL_SDRAM_DMA_XferCpltCallback) for HAL_SDRAM_DMA_XferCpltCallback
    stm32f4xx_hal_sdram.o(i.HAL_SDRAM_Write_DMA) refers to stm32f4xx_hal_sdram.o(i.HAL_SDRAM_DMA_XferErrorCallback) for HAL_SDRAM_DMA_XferErrorCallback
    stm32f4xx_hal_sram.o(i.HAL_SRAM_DeInit) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_MspDeInit) for HAL_SRAM_MspDeInit
    stm32f4xx_hal_sram.o(i.HAL_SRAM_DeInit) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_DeInit) for FMC_NORSRAM_DeInit
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to lcd.o(i.HAL_SRAM_MspInit) for HAL_SRAM_MspInit
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Init) for FMC_NORSRAM_Init
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Timing_Init) for FMC_NORSRAM_Timing_Init
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Extended_Timing_Init) for FMC_NORSRAM_Extended_Timing_Init
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferCpltCallback) for HAL_SRAM_DMA_XferCpltCallback
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferErrorCallback) for HAL_SRAM_DMA_XferErrorCallback
    stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Disable) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_WriteOperation_Disable) for FMC_NORSRAM_WriteOperation_Disable
    stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Enable) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_WriteOperation_Enable) for FMC_NORSRAM_WriteOperation_Enable
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferCpltCallback) for HAL_SRAM_DMA_XferCpltCallback
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferErrorCallback) for HAL_SRAM_DMA_XferErrorCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI3_SetConfig) for TIM_TI3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI4_SetConfig) for TIM_TI4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_timebase_tim_template.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutationCallback) for HAL_TIMEx_CommutationCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchronization) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchronization_IT) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAError) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to stm32f4xx_hal_timebase_tim_template.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutationEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutationEvent_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutationCallback) for HAL_TIMEx_CommutationCallback
    stm32f4xx_hal_timebase_tim_template.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal_timebase_tim_template.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    stm32f4xx_hal_timebase_tim_template.o(i.HAL_InitTick) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig) for HAL_RCC_GetClockConfig
    stm32f4xx_hal_timebase_tim_template.o(i.HAL_InitTick) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_timebase_tim_template.o(i.HAL_InitTick) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    stm32f4xx_hal_timebase_tim_template.o(i.HAL_InitTick) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    stm32f4xx_hal_timebase_tim_template.o(i.HAL_InitTick) refers to stm32f4xx_hal_timebase_tim_template.o(.bss) for TimHandle
    stm32f4xx_hal_timebase_tim_template.o(i.HAL_ResumeTick) refers to stm32f4xx_hal_timebase_tim_template.o(.bss) for TimHandle
    stm32f4xx_hal_timebase_tim_template.o(i.HAL_SuspendTick) refers to stm32f4xx_hal_timebase_tim_template.o(.bss) for TimHandle
    stm32f4xx_hal_timebase_tim_template.o(i.HAL_TIM_PeriodElapsedCallback) refers to stm32f4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f4xx_hal_timebase_tim_template.o(i.TIM6_DAC_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f4xx_hal_timebase_tim_template.o(i.TIM6_DAC_IRQHandler) refers to stm32f4xx_hal_timebase_tim_template.o(.bss) for TimHandle
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_DeInit) refers to stm32f4xx_hal_uart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Transmit_IT) for UART_Transmit_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT) for UART_EndTransmit_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to usart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to usart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_usart.o(i.HAL_USART_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_usart.o(i.HAL_USART_DeInit) refers to stm32f4xx_hal_usart.o(i.HAL_USART_MspDeInit) for HAL_USART_MspDeInit
    stm32f4xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32f4xx_hal_usart.o(i.HAL_USART_ErrorCallback) for HAL_USART_ErrorCallback
    stm32f4xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32f4xx_hal_usart.o(i.USART_Receive_IT) for USART_Receive_IT
    stm32f4xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32f4xx_hal_usart.o(i.USART_TransmitReceive_IT) for USART_TransmitReceive_IT
    stm32f4xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32f4xx_hal_usart.o(i.USART_Transmit_IT) for USART_Transmit_IT
    stm32f4xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32f4xx_hal_usart.o(i.USART_EndTransmit_IT) for USART_EndTransmit_IT
    stm32f4xx_hal_usart.o(i.HAL_USART_Init) refers to stm32f4xx_hal_usart.o(i.HAL_USART_MspInit) for HAL_USART_MspInit
    stm32f4xx_hal_usart.o(i.HAL_USART_Init) refers to stm32f4xx_hal_usart.o(i.USART_SetConfig) for USART_SetConfig
    stm32f4xx_hal_usart.o(i.HAL_USART_Receive) refers to stm32f4xx_hal_usart.o(i.USART_WaitOnFlagUntilTimeout) for USART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_usart.o(i.HAL_USART_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_usart.o(i.HAL_USART_Receive_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMAReceiveCplt) for USART_DMAReceiveCplt
    stm32f4xx_hal_usart.o(i.HAL_USART_Receive_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMARxHalfCplt) for USART_DMARxHalfCplt
    stm32f4xx_hal_usart.o(i.HAL_USART_Receive_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMAError) for USART_DMAError
    stm32f4xx_hal_usart.o(i.HAL_USART_Transmit) refers to stm32f4xx_hal_usart.o(i.USART_WaitOnFlagUntilTimeout) for USART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive) refers to stm32f4xx_hal_usart.o(i.USART_WaitOnFlagUntilTimeout) for USART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMAReceiveCplt) for USART_DMAReceiveCplt
    stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMARxHalfCplt) for USART_DMARxHalfCplt
    stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMATransmitCplt) for USART_DMATransmitCplt
    stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMATxHalfCplt) for USART_DMATxHalfCplt
    stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMAError) for USART_DMAError
    stm32f4xx_hal_usart.o(i.HAL_USART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_usart.o(i.HAL_USART_Transmit_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMATransmitCplt) for USART_DMATransmitCplt
    stm32f4xx_hal_usart.o(i.HAL_USART_Transmit_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMATxHalfCplt) for USART_DMATxHalfCplt
    stm32f4xx_hal_usart.o(i.HAL_USART_Transmit_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMAError) for USART_DMAError
    stm32f4xx_hal_usart.o(i.USART_DMAError) refers to stm32f4xx_hal_usart.o(i.HAL_USART_ErrorCallback) for HAL_USART_ErrorCallback
    stm32f4xx_hal_usart.o(i.USART_DMAReceiveCplt) refers to stm32f4xx_hal_usart.o(i.HAL_USART_RxCpltCallback) for HAL_USART_RxCpltCallback
    stm32f4xx_hal_usart.o(i.USART_DMAReceiveCplt) refers to stm32f4xx_hal_usart.o(i.HAL_USART_TxRxCpltCallback) for HAL_USART_TxRxCpltCallback
    stm32f4xx_hal_usart.o(i.USART_DMARxHalfCplt) refers to stm32f4xx_hal_usart.o(i.HAL_USART_RxHalfCpltCallback) for HAL_USART_RxHalfCpltCallback
    stm32f4xx_hal_usart.o(i.USART_DMATransmitCplt) refers to stm32f4xx_hal_usart.o(i.HAL_USART_TxCpltCallback) for HAL_USART_TxCpltCallback
    stm32f4xx_hal_usart.o(i.USART_DMATxHalfCplt) refers to stm32f4xx_hal_usart.o(i.HAL_USART_TxHalfCpltCallback) for HAL_USART_TxHalfCpltCallback
    stm32f4xx_hal_usart.o(i.USART_EndTransmit_IT) refers to stm32f4xx_hal_usart.o(i.HAL_USART_TxCpltCallback) for HAL_USART_TxCpltCallback
    stm32f4xx_hal_usart.o(i.USART_Receive_IT) refers to stm32f4xx_hal_usart.o(i.HAL_USART_RxCpltCallback) for HAL_USART_RxCpltCallback
    stm32f4xx_hal_usart.o(i.USART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_usart.o(i.USART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_usart.o(i.USART_TransmitReceive_IT) refers to stm32f4xx_hal_usart.o(i.HAL_USART_TxRxCpltCallback) for HAL_USART_TxRxCpltCallback
    stm32f4xx_hal_usart.o(i.USART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_ll_fmc.o(i.FMC_NAND_GetECC) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_ll_fmc.o(i.FMC_SDRAM_SendCommand) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32f4xx_hal_dma.o(.constdata) for flagBitshiftOffset
    stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma2d.o(i.HAL_DMA2D_Abort) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma2d.o(i.HAL_DMA2D_BlendingStart) refers to stm32f4xx_hal_dma2d.o(i.DMA2D_SetConfig) for DMA2D_SetConfig
    stm32f4xx_hal_dma2d.o(i.HAL_DMA2D_BlendingStart_IT) refers to stm32f4xx_hal_dma2d.o(i.DMA2D_SetConfig) for DMA2D_SetConfig
    stm32f4xx_hal_dma2d.o(i.HAL_DMA2D_DeInit) refers to stm32f4xx_hal_dma2d.o(i.HAL_DMA2D_MspDeInit) for HAL_DMA2D_MspDeInit
    stm32f4xx_hal_dma2d.o(i.HAL_DMA2D_IRQHandler) refers to stm32f4xx_hal_dma2d.o(i.HAL_DMA2D_LineEventCallback) for HAL_DMA2D_LineEventCallback
    stm32f4xx_hal_dma2d.o(i.HAL_DMA2D_IRQHandler) refers to stm32f4xx_hal_dma2d.o(i.HAL_DMA2D_CLUTLoadingCpltCallback) for HAL_DMA2D_CLUTLoadingCpltCallback
    stm32f4xx_hal_dma2d.o(i.HAL_DMA2D_Init) refers to stm32f4xx_hal_dma2d.o(i.HAL_DMA2D_MspInit) for HAL_DMA2D_MspInit
    stm32f4xx_hal_dma2d.o(i.HAL_DMA2D_PollForTransfer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma2d.o(i.HAL_DMA2D_Start) refers to stm32f4xx_hal_dma2d.o(i.DMA2D_SetConfig) for DMA2D_SetConfig
    stm32f4xx_hal_dma2d.o(i.HAL_DMA2D_Start_IT) refers to stm32f4xx_hal_dma2d.o(i.DMA2D_SetConfig) for DMA2D_SetConfig
    stm32f4xx_hal_dma2d.o(i.HAL_DMA2D_Suspend) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_nor.o(i.HAL_NOR_DeInit) refers to stm32f4xx_hal_nor.o(i.HAL_NOR_MspDeInit) for HAL_NOR_MspDeInit
    stm32f4xx_hal_nor.o(i.HAL_NOR_DeInit) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_DeInit) for FMC_NORSRAM_DeInit
    stm32f4xx_hal_nor.o(i.HAL_NOR_GetStatus) refers to stm32f4xx_hal_nor.o(i.HAL_NOR_MspWait) for HAL_NOR_MspWait
    stm32f4xx_hal_nor.o(i.HAL_NOR_GetStatus) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_nor.o(i.HAL_NOR_Init) refers to stm32f4xx_hal_nor.o(i.HAL_NOR_MspInit) for HAL_NOR_MspInit
    stm32f4xx_hal_nor.o(i.HAL_NOR_Init) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Init) for FMC_NORSRAM_Init
    stm32f4xx_hal_nor.o(i.HAL_NOR_Init) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Timing_Init) for FMC_NORSRAM_Timing_Init
    stm32f4xx_hal_nor.o(i.HAL_NOR_Init) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Extended_Timing_Init) for FMC_NORSRAM_Extended_Timing_Init
    stm32f4xx_hal_nor.o(i.HAL_NOR_WriteOperation_Disable) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_WriteOperation_Disable) for FMC_NORSRAM_WriteOperation_Disable
    stm32f4xx_hal_nor.o(i.HAL_NOR_WriteOperation_Enable) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_WriteOperation_Enable) for FMC_NORSRAM_WriteOperation_Enable
    stm32f4xx_hal_i2c.o(i.HAL_I2C_DeInit) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) for I2C_Slave_AF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) for I2C_MasterTransmit_TXE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) for I2C_MasterTransmit_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) for I2C_MasterReceive_RXNE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) for I2C_MasterReceive_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR) for I2C_Slave_ADDR
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) for I2C_Slave_STOPF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_SlaveTransmit_TXE) for I2C_SlaveTransmit_TXE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_SlaveTransmit_BTF) for I2C_SlaveTransmit_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_SlaveReceive_RXNE) for I2C_SlaveReceive_RXNE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_SlaveReceive_BTF) for I2C_SlaveReceive_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) for I2C_MasterRequestRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) for I2C_MasterRequestRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) for I2C_DMAMasterReceiveCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) for I2C_MasterRequestRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Sequential_Receive_IT) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Sequential_Receive_IT) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) for I2C_MasterRequestRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Sequential_Transmit_IT) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Sequential_Transmit_IT) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) for I2C_MasterRequestWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) for I2C_MasterRequestWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) for I2C_MasterRequestWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) for I2C_DMAMasterTransmitCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) for I2C_MasterRequestWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAMemReceiveCplt) for I2C_DMAMemReceiveCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAMemTransmitCplt) for I2C_DMAMemTransmitCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt) for I2C_DMASlaveReceiveCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt) for I2C_DMASlaveTransmitCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAMemReceiveCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAMemReceiveCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAMemTransmitCplt) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_DMAMemTransmitCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAMemTransmitCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_SlaveReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_SlaveTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_spi.o(i.HAL_SPI_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_spi.o(i.HAL_SPI_DeInit) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_MspDeInit) for HAL_SPI_MspDeInit
    stm32f4xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.HAL_SPI_Init) refers to spi.o(i.HAL_SPI_MspInit) for HAL_SPI_MspInit
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) for HAL_SPI_TransmitReceive
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f4xx_hal_spi.o(i.SPI_CheckForDisablingSPI) for SPI_CheckForDisablingSPI
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f4xx_hal_spi.o(i.SPI_CheckFlag_BSY) for SPI_CheckFlag_BSY
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) for HAL_SPI_TransmitReceive_DMA
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) for HAL_SPI_TransmitReceive_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_RxISR_16BIT) for SPI_RxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_RxISR_8BIT) for SPI_RxISR_8BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32f4xx_hal_spi.o(i.SPI_CheckFlag_BSY) for SPI_CheckFlag_BSY
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32f4xx_hal_spi.o(i.SPI_CheckFlag_BSY) for SPI_CheckFlag_BSY
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) for SPI_DMAHalfTransmitReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) for SPI_DMATransmitReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) for SPI_2linesRxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) for SPI_2linesTxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) for SPI_2linesRxISR_8BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) for SPI_2linesTxISR_8BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) for SPI_DMAHalfTransmitCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) for SPI_DMATransmitCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_IT) refers to stm32f4xx_hal_spi.o(i.SPI_TxISR_16BIT) for SPI_TxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_IT) refers to stm32f4xx_hal_spi.o(i.SPI_TxISR_8BIT) for SPI_TxISR_8BIT
    stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_CheckFlag_BSY) refers to stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(i.SPI_CheckFlag_BSY) for SPI_CheckFlag_BSY
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f4xx_hal_spi.o(i.SPI_CheckForDisablingSPI) for SPI_CheckForDisablingSPI
    stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f4xx_hal_spi.o(i.SPI_CheckFlag_BSY) for SPI_CheckFlag_BSY
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAError) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxHalfCpltCallback) for HAL_SPI_RxHalfCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxHalfCpltCallback) for HAL_SPI_TxHalfCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxHalfCpltCallback) for HAL_SPI_TxRxHalfCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f4xx_hal_spi.o(i.SPI_CheckForDisablingSPI) for SPI_CheckForDisablingSPI
    stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f4xx_hal_spi.o(i.SPI_CheckFlag_BSY) for SPI_CheckFlag_BSY
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.SPI_CheckFlag_BSY) for SPI_CheckFlag_BSY
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_RxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32f4xx_hal_spi.o(i.SPI_RxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32f4xx_hal_spi.o(i.SPI_TxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_TxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    startup_stm32f429xx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f429xx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f429xx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f429xx.o(RESET) refers to startup_stm32f429xx.o(STACK) for __initial_sp
    startup_stm32f429xx.o(RESET) refers to startup_stm32f429xx.o(.text) for Reset_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f429xx.o(RESET) refers to usart.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_hal_timebase_tim_template.o(i.TIM6_DAC_IRQHandler) for TIM6_DAC_IRQHandler
    startup_stm32f429xx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f429xx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f429xx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f429xx.o(.text) refers to startup_stm32f429xx.o(HEAP) for Heap_Mem
    startup_stm32f429xx.o(.text) refers to startup_stm32f429xx.o(STACK) for Stack_Mem
    use_no_semi_2.o(.text) refers (Special) to use_no_semi.o(.text) for __use_no_semihosting_swi
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f429xx.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to usart.o(i._sys_exit) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    defsig_exit.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (192 bytes).
    Removing stm32f429_winner.o(.rev16_text), (4 bytes).
    Removing stm32f429_winner.o(.revsh_text), (4 bytes).
    Removing stm32f429_winner.o(.rrx_text), (6 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing delay.o(.rrx_text), (6 bytes).
    Removing delay.o(i.SysTick_clkconfig), (20 bytes).
    Removing delay.o(i.SysTick_delay_ms), (24 bytes).
    Removing delay.o(i.SysTick_delay_us), (72 bytes).
    Removing delay.o(i.delay_20ns), (10 bytes).
    Removing delay.o(i.delay_ms), (24 bytes).
    Removing delay.o(i.delay_us), (24 bytes).
    Removing delay.o(.data), (4 bytes).
    Removing led.o(.rev16_text), (4 bytes).
    Removing led.o(.revsh_text), (4 bytes).
    Removing led.o(.rrx_text), (6 bytes).
    Removing lcd.o(.rev16_text), (4 bytes).
    Removing lcd.o(.revsh_text), (4 bytes).
    Removing lcd.o(.rrx_text), (6 bytes).
    Removing lcd.o(i.HAL_SRAM_MspInit), (312 bytes).
    Removing lcd.o(i.LCD_BGR2RGB), (26 bytes).
    Removing lcd.o(i.LCD_Clear), (44 bytes).
    Removing lcd.o(i.LCD_Color_Fill), (224 bytes).
    Removing lcd.o(i.LCD_DisplayOff), (92 bytes).
    Removing lcd.o(i.LCD_DisplayOn), (88 bytes).
    Removing lcd.o(i.LCD_Display_Dir), (564 bytes).
    Removing lcd.o(i.LCD_DrawLine), (176 bytes).
    Removing lcd.o(i.LCD_DrawPoint), (92 bytes).
    Removing lcd.o(i.LCD_DrawRectangle), (64 bytes).
    Removing lcd.o(i.LCD_Draw_Circle), (152 bytes).
    Removing lcd.o(i.LCD_Fast_DrawPoint), (212 bytes).
    Removing lcd.o(i.LCD_Fill), (200 bytes).
    Removing lcd.o(i.LCD_Init), (5344 bytes).
    Removing lcd.o(i.LCD_Pow), (22 bytes).
    Removing lcd.o(i.LCD_RD_DATA), (20 bytes).
    Removing lcd.o(i.LCD_ReadPoint), (112 bytes).
    Removing lcd.o(i.LCD_ReadReg), (22 bytes).
    Removing lcd.o(i.LCD_SetCursor), (176 bytes).
    Removing lcd.o(i.LCD_Set_Window), (584 bytes).
    Removing lcd.o(i.LCD_ShowChar), (316 bytes).
    Removing lcd.o(i.LCD_ShowNum), (148 bytes).
    Removing lcd.o(i.LCD_ShowString), (128 bytes).
    Removing lcd.o(i.LCD_ShowxNum), (190 bytes).
    Removing lcd.o(i.LCD_WR_DATA), (24 bytes).
    Removing lcd.o(i.LCD_WR_REG), (24 bytes).
    Removing lcd.o(i.LCD_WriteRAM), (12 bytes).
    Removing lcd.o(i.LCD_WriteRAM_Prepare), (20 bytes).
    Removing lcd.o(i.LCD_WriteReg), (16 bytes).
    Removing lcd.o(i.gui_draw_hline), (36 bytes).
    Removing lcd.o(i.gui_fill_circle), (176 bytes).
    Removing lcd.o(i.lcd_draw_bline), (212 bytes).
    Removing lcd.o(i.my_abs), (18 bytes).
    Removing lcd.o(i.opt_delay), (14 bytes).
    Removing lcd.o(.bss), (96 bytes).
    Removing lcd.o(.constdata), (18240 bytes).
    Removing lcd.o(.data), (8 bytes).
    Removing ltdc.o(.rev16_text), (4 bytes).
    Removing ltdc.o(.revsh_text), (4 bytes).
    Removing ltdc.o(.rrx_text), (6 bytes).
    Removing ltdc.o(i.HAL_LTDC_MspInit), (388 bytes).
    Removing ltdc.o(i.LCD_GPIO_Config), (168 bytes).
    Removing ltdc.o(i.LCD_WriteByteSPI), (76 bytes).
    Removing ltdc.o(i.LTDC_Clear), (36 bytes).
    Removing ltdc.o(i.LTDC_Clk_Set), (38 bytes).
    Removing ltdc.o(i.LTDC_Color_Fill), (86 bytes).
    Removing ltdc.o(i.LTDC_Display_Dir), (92 bytes).
    Removing ltdc.o(i.LTDC_Draw_Point), (412 bytes).
    Removing ltdc.o(i.LTDC_Fill), (860 bytes).
    Removing ltdc.o(i.LTDC_Init), (712 bytes).
    Removing ltdc.o(i.LTDC_Layer_Parameter_Config), (124 bytes).
    Removing ltdc.o(i.LTDC_Layer_Switch), (80 bytes).
    Removing ltdc.o(i.LTDC_Layer_Window_Config), (48 bytes).
    Removing ltdc.o(i.LTDC_Read_Point), (416 bytes).
    Removing ltdc.o(i.LTDC_Select_Layer), (12 bytes).
    Removing ltdc.o(i.LTDC_Switch), (48 bytes).
    Removing ltdc.o(i.Lcd_Initialize), (5192 bytes).
    Removing ltdc.o(i.SPI_WriteComm), (116 bytes).
    Removing ltdc.o(i.SPI_WriteData), (64 bytes).
    Removing ltdc.o(.bss), (264 bytes).
    Removing ltdc.o(.data), (8 bytes).
    Removing sdram.o(.rev16_text), (4 bytes).
    Removing sdram.o(.revsh_text), (4 bytes).
    Removing sdram.o(.rrx_text), (6 bytes).
    Removing sdram.o(i.FMC_SDRAM_ReadBuffer), (22 bytes).
    Removing sdram.o(i.FMC_SDRAM_WriteBuffer), (24 bytes).
    Removing sdram.o(i.HAL_SDRAM_MspInit), (308 bytes).
    Removing sdram.o(i.SDRAM_Init), (116 bytes).
    Removing sdram.o(i.SDRAM_Initialization_Sequence), (68 bytes).
    Removing sdram.o(i.SDRAM_Send_Cmd), (72 bytes).
    Removing sdram.o(.bss), (52 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspInit), (188 bytes).
    Removing usart.o(i.fputc), (28 bytes).
    Removing usart.o(i.uart_init), (56 bytes).
    Removing spi.o(.rev16_text), (4 bytes).
    Removing spi.o(.revsh_text), (4 bytes).
    Removing spi.o(.rrx_text), (6 bytes).
    Removing spi.o(i.SPI5_SetSpeed), (72 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing key.o(.rrx_text), (6 bytes).
    Removing key.o(i.BSP_KEY_Init), (200 bytes).
    Removing key.o(i.KEY_Init), (28 bytes).
    Removing key.o(i.KEY_Scan), (200 bytes).
    Removing key.o(.constdata), (8 bytes).
    Removing key.o(.data), (17 bytes).
    Removing dac8563.o(.rev16_text), (4 bytes).
    Removing dac8563.o(.revsh_text), (4 bytes).
    Removing dac8563.o(.rrx_text), (6 bytes).
    Removing dac8563.o(i.DAC8563_Write_CHA), (14 bytes).
    Removing dac8563.o(i.DAC8563_Write_CHB), (14 bytes).
    Removing dac8563.o(.bss), (88 bytes).
    Removing dac8563.o(.data), (2 bytes).
    Removing stm32f4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (20 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (20 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (20 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (20 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (20 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (20 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DeInit), (68 bytes).
    Removing stm32f4xx_hal.o(i.HAL_Delay), (26 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableCompensationCell), (28 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableMemorySwappingBank), (28 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableCompensationCell), (28 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableMemorySwappingBank), (24 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_InitTick), (36 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_ResumeTick), (18 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SuspendTick), (18 bytes).
    Removing stm32f4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (92 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (40 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (40 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (148 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (68 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (40 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config), (52 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (384 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (46 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin), (16 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (180 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DeInit), (28 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (28 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (20 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (20 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (20 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (20 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (32 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (40 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (64 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (32 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (228 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (76 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (28 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableOverDrive), (168 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (76 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (24 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterUnderDriveSTOPMode), (168 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DeInit), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (292 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq), (44 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (192 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (32 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (268 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (872 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit), (112 bytes).
    Removing stm32f4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Byte), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord), (44 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord), (40 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Word), (40 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode), (180 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation), (112 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (284 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (28 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (44 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program), (152 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (136 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock), (44 bytes).
    Removing stm32f4xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector), (108 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches), (120 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase), (84 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BOR_LevelConfig), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BootConfig), (48 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisablePCROP), (100 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP), (148 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnablePCROP), (104 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP), (140 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetBOR), (16 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetRDP), (36 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetUser), (16 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetWRP), (12 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig), (60 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBGetConfig), (28 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBProgram), (68 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (176 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (120 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (34 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (148 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OB_DeSelectPCROP), (24 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OB_GetBank2WRP), (12 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OB_SelectPCROP), (28 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_ltdc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_ltdc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_ltdc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_ltdc.o(i.HAL_LTDC_ConfigCLUT), (180 bytes).
    Removing stm32f4xx_hal_ltdc.o(i.HAL_LTDC_ConfigColorKeying), (96 bytes).
    Removing stm32f4xx_hal_ltdc.o(i.HAL_LTDC_ConfigLayer), (94 bytes).
    Removing stm32f4xx_hal_ltdc.o(i.HAL_LTDC_DeInit), (30 bytes).
    Removing stm32f4xx_hal_ltdc.o(i.HAL_LTDC_DisableCLUT), (76 bytes).
    Removing stm32f4xx_hal_ltdc.o(i.HAL_LTDC_DisableColorKeying), (76 bytes).
    Removing stm32f4xx_hal_ltdc.o(i.HAL_LTDC_DisableDither), (68 bytes).
    Removing stm32f4xx_hal_ltdc.o(i.HAL_LTDC_EnableCLUT), (76 bytes).
    Removing stm32f4xx_hal_ltdc.o(i.HAL_LTDC_EnableColorKeying), (76 bytes).
    Removing stm32f4xx_hal_ltdc.o(i.HAL_LTDC_EnableDither), (68 bytes).
    Removing stm32f4xx_hal_ltdc.o(i.HAL_LTDC_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_ltdc.o(i.HAL_LTDC_GetError), (8 bytes).
    Removing stm32f4xx_hal_ltdc.o(i.HAL_LTDC_GetState), (8 bytes).
    Removing stm32f4xx_hal_ltdc.o(i.HAL_LTDC_IRQHandler), (208 bytes).
    Removing stm32f4xx_hal_ltdc.o(i.HAL_LTDC_Init), (296 bytes).
    Removing stm32f4xx_hal_ltdc.o(i.HAL_LTDC_LineEventCallback), (2 bytes).
    Removing stm32f4xx_hal_ltdc.o(i.HAL_LTDC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_ltdc.o(i.HAL_LTDC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_ltdc.o(i.HAL_LTDC_ProgramLineEvent), (72 bytes).
    Removing stm32f4xx_hal_ltdc.o(i.HAL_LTDC_SetAddress), (92 bytes).
    Removing stm32f4xx_hal_ltdc.o(i.HAL_LTDC_SetAlpha), (92 bytes).
    Removing stm32f4xx_hal_ltdc.o(i.HAL_LTDC_SetPitch), (208 bytes).
    Removing stm32f4xx_hal_ltdc.o(i.HAL_LTDC_SetPixelFormat), (92 bytes).
    Removing stm32f4xx_hal_ltdc.o(i.HAL_LTDC_SetWindowPosition), (108 bytes).
    Removing stm32f4xx_hal_ltdc.o(i.HAL_LTDC_SetWindowSize), (116 bytes).
    Removing stm32f4xx_hal_ltdc.o(i.LTDC_SetConfig), (530 bytes).
    Removing stm32f4xx_hal_ltdc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_ltdc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_ltdc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_sdram.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_sdram.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_sdram.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_sdram.o(i.HAL_SDRAM_DMA_XferCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_sdram.o(i.HAL_SDRAM_DMA_XferErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_sdram.o(i.HAL_SDRAM_DeInit), (34 bytes).
    Removing stm32f4xx_hal_sdram.o(i.HAL_SDRAM_GetModeStatus), (14 bytes).
    Removing stm32f4xx_hal_sdram.o(i.HAL_SDRAM_GetState), (8 bytes).
    Removing stm32f4xx_hal_sdram.o(i.HAL_SDRAM_IRQHandler), (34 bytes).
    Removing stm32f4xx_hal_sdram.o(i.HAL_SDRAM_Init), (64 bytes).
    Removing stm32f4xx_hal_sdram.o(i.HAL_SDRAM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_sdram.o(i.HAL_SDRAM_MspInit), (2 bytes).
    Removing stm32f4xx_hal_sdram.o(i.HAL_SDRAM_ProgramRefreshRate), (40 bytes).
    Removing stm32f4xx_hal_sdram.o(i.HAL_SDRAM_Read_16b), (80 bytes).
    Removing stm32f4xx_hal_sdram.o(i.HAL_SDRAM_Read_32b), (80 bytes).
    Removing stm32f4xx_hal_sdram.o(i.HAL_SDRAM_Read_8b), (80 bytes).
    Removing stm32f4xx_hal_sdram.o(i.HAL_SDRAM_Read_DMA), (104 bytes).
    Removing stm32f4xx_hal_sdram.o(i.HAL_SDRAM_RefreshErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_sdram.o(i.HAL_SDRAM_SendCommand), (58 bytes).
    Removing stm32f4xx_hal_sdram.o(i.HAL_SDRAM_SetAutoRefreshNumber), (40 bytes).
    Removing stm32f4xx_hal_sdram.o(i.HAL_SDRAM_WriteProtection_Disable), (38 bytes).
    Removing stm32f4xx_hal_sdram.o(i.HAL_SDRAM_WriteProtection_Enable), (38 bytes).
    Removing stm32f4xx_hal_sdram.o(i.HAL_SDRAM_Write_16b), (86 bytes).
    Removing stm32f4xx_hal_sdram.o(i.HAL_SDRAM_Write_32b), (86 bytes).
    Removing stm32f4xx_hal_sdram.o(i.HAL_SDRAM_Write_8b), (86 bytes).
    Removing stm32f4xx_hal_sdram.o(i.HAL_SDRAM_Write_DMA), (108 bytes).
    Removing stm32f4xx_hal_sram.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_sram.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_sram.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_DeInit), (34 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_GetState), (8 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Init), (88 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_MspInit), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_16b), (70 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_32b), (68 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_8b), (70 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA), (96 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Disable), (60 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Enable), (54 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_16b), (82 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_32b), (80 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_8b), (82 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA), (108 bytes).
    Removing stm32f4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (70 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start), (30 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (104 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (62 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource), (306 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (226 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (24 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (388 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (128 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (388 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (128 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (70 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init), (180 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start), (78 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (328 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (126 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (104 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (152 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (152 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (54 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (224 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (70 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init), (52 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start), (32 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (268 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (108 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop), (58 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (140 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (134 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (116 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (70 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init), (52 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (312 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (152 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop), (128 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (212 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (204 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (286 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (70 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (100 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (140 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (164 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel), (264 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (70 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init), (52 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (312 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (152 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (136 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (212 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (204 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (84 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchronization), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchronization_IT), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd), (28 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt), (70 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (70 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAError), (20 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (20 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt), (20 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig), (24 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig), (20 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig), (96 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (164 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage), (42 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig), (152 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage), (44 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig), (62 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI3_SetConfig), (60 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI4_SetConfig), (64 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime), (80 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutationEvent), (110 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutationEvent_DMA), (144 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutationEvent_IT), (122 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (70 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (196 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (30 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (112 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (42 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (56 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (68 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (68 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization), (98 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (44 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (280 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (132 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (96 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (178 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (196 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (32 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (56 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (96 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (120 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (44 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (280 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (132 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (96 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (178 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (196 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (48 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (20 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (28 bytes).
    Removing stm32f4xx_hal_timebase_tim_template.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_timebase_tim_template.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_timebase_tim_template.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_timebase_tim_template.o(i.HAL_ResumeTick), (24 bytes).
    Removing stm32f4xx_hal_timebase_tim_template.o(i.HAL_SuspendTick), (24 bytes).
    Removing stm32f4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (68 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (68 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (118 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_Init), (142 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_SendBreak), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (154 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAPause), (78 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAResume), (102 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DeInit), (46 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetError), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Init), (114 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive), (204 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (136 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit), (196 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (148 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (88 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMAError), (38 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt), (46 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt), (14 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt), (54 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt), (14 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_SetConfig), (872 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout), (260 bytes).
    Removing stm32f4xx_hal_usart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_usart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_usart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_DMAPause), (50 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_DMAResume), (50 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_DMAStop), (58 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_DeInit), (54 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_GetError), (6 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_GetState), (8 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_IRQHandler), (336 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_Init), (90 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_Receive), (256 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_Receive_DMA), (196 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_Receive_IT), (120 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_RxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_Transmit), (196 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive), (294 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA), (232 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive_IT), (132 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_Transmit_DMA), (148 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_Transmit_IT), (88 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_TxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_TxRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_DMAError), (34 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_DMAReceiveCplt), (116 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_DMARxHalfCplt), (14 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_DMATransmitCplt), (70 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_DMATxHalfCplt), (14 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_EndTransmit_IT), (44 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_Receive_IT), (194 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_SetConfig), (332 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_TransmitReceive_IT), (270 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_Transmit_IT), (104 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_WaitOnFlagUntilTimeout), (250 bytes).
    Removing stm32f4xx_ll_fmc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_ll_fmc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_ll_fmc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_AttributeSpace_Timing_Init), (54 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_CommonSpace_Timing_Init), (54 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_DeInit), (66 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_ECC_Disable), (28 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_ECC_Enable), (28 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_GetECC), (88 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_Init), (76 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_DeInit), (56 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Extended_Timing_Init), (72 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Init), (116 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Timing_Init), (94 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_WriteOperation_Disable), (18 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_WriteOperation_Enable), (18 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_PCCARD_AttributeSpace_Timing_Init), (38 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_PCCARD_CommonSpace_Timing_Init), (38 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_PCCARD_DeInit), (32 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_PCCARD_IOSpace_Timing_Init), (38 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_PCCARD_Init), (44 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_DeInit), (32 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_GetModeStatus), (24 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_Init), (116 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_ProgramRefreshRate), (14 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_SendCommand), (82 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_SetAutoRefreshNumber), (14 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_Timing_Init), (152 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_WriteProtection_Disable), (18 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_WriteProtection_Enable), (18 bytes).
    Removing stm32f4xx_ll_fsmc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_ll_fsmc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_ll_fsmc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift), (52 bytes).
    Removing stm32f4xx_hal_dma.o(i.DMA_SetConfig), (44 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Abort), (94 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit), (100 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetError), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetState), (8 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler), (536 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Init), (132 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (404 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start), (82 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT), (106 bytes).
    Removing stm32f4xx_hal_dma.o(.constdata), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (32 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (18 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (130 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (190 bytes).
    Removing stm32f4xx_hal_dma2d.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma2d.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma2d.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma2d.o(i.DMA2D_SetConfig), (230 bytes).
    Removing stm32f4xx_hal_dma2d.o(i.HAL_DMA2D_Abort), (100 bytes).
    Removing stm32f4xx_hal_dma2d.o(i.HAL_DMA2D_BlendingStart), (82 bytes).
    Removing stm32f4xx_hal_dma2d.o(i.HAL_DMA2D_BlendingStart_IT), (94 bytes).
    Removing stm32f4xx_hal_dma2d.o(i.HAL_DMA2D_CLUTLoad_IT), (156 bytes).
    Removing stm32f4xx_hal_dma2d.o(i.HAL_DMA2D_CLUTLoadingCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_dma2d.o(i.HAL_DMA2D_ConfigCLUT), (124 bytes).
    Removing stm32f4xx_hal_dma2d.o(i.HAL_DMA2D_ConfigLayer), (284 bytes).
    Removing stm32f4xx_hal_dma2d.o(i.HAL_DMA2D_DeInit), (36 bytes).
    Removing stm32f4xx_hal_dma2d.o(i.HAL_DMA2D_DisableCLUT), (34 bytes).
    Removing stm32f4xx_hal_dma2d.o(i.HAL_DMA2D_EnableCLUT), (62 bytes).
    Removing stm32f4xx_hal_dma2d.o(i.HAL_DMA2D_GetError), (6 bytes).
    Removing stm32f4xx_hal_dma2d.o(i.HAL_DMA2D_GetState), (8 bytes).
    Removing stm32f4xx_hal_dma2d.o(i.HAL_DMA2D_IRQHandler), (398 bytes).
    Removing stm32f4xx_hal_dma2d.o(i.HAL_DMA2D_Init), (98 bytes).
    Removing stm32f4xx_hal_dma2d.o(i.HAL_DMA2D_LineEventCallback), (2 bytes).
    Removing stm32f4xx_hal_dma2d.o(i.HAL_DMA2D_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_dma2d.o(i.HAL_DMA2D_MspInit), (2 bytes).
    Removing stm32f4xx_hal_dma2d.o(i.HAL_DMA2D_PollForTransfer), (372 bytes).
    Removing stm32f4xx_hal_dma2d.o(i.HAL_DMA2D_ProgramLineEvent), (60 bytes).
    Removing stm32f4xx_hal_dma2d.o(i.HAL_DMA2D_Resume), (36 bytes).
    Removing stm32f4xx_hal_dma2d.o(i.HAL_DMA2D_Start), (78 bytes).
    Removing stm32f4xx_hal_dma2d.o(i.HAL_DMA2D_Start_IT), (90 bytes).
    Removing stm32f4xx_hal_dma2d.o(i.HAL_DMA2D_Suspend), (100 bytes).
    Removing stm32f4xx_hal_nor.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_nor.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_nor.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_nor.o(i.HAL_NOR_DeInit), (34 bytes).
    Removing stm32f4xx_hal_nor.o(i.HAL_NOR_Erase_Block), (138 bytes).
    Removing stm32f4xx_hal_nor.o(i.HAL_NOR_Erase_Chip), (138 bytes).
    Removing stm32f4xx_hal_nor.o(i.HAL_NOR_GetState), (8 bytes).
    Removing stm32f4xx_hal_nor.o(i.HAL_NOR_GetStatus), (132 bytes).
    Removing stm32f4xx_hal_nor.o(i.HAL_NOR_Init), (94 bytes).
    Removing stm32f4xx_hal_nor.o(i.HAL_NOR_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_nor.o(i.HAL_NOR_MspInit), (2 bytes).
    Removing stm32f4xx_hal_nor.o(i.HAL_NOR_MspWait), (2 bytes).
    Removing stm32f4xx_hal_nor.o(i.HAL_NOR_Program), (126 bytes).
    Removing stm32f4xx_hal_nor.o(i.HAL_NOR_ProgramBuffer), (172 bytes).
    Removing stm32f4xx_hal_nor.o(i.HAL_NOR_Read), (122 bytes).
    Removing stm32f4xx_hal_nor.o(i.HAL_NOR_ReadBuffer), (134 bytes).
    Removing stm32f4xx_hal_nor.o(i.HAL_NOR_Read_CFI), (124 bytes).
    Removing stm32f4xx_hal_nor.o(i.HAL_NOR_Read_ID), (136 bytes).
    Removing stm32f4xx_hal_nor.o(i.HAL_NOR_ReturnToReadMode), (98 bytes).
    Removing stm32f4xx_hal_nor.o(i.HAL_NOR_WriteOperation_Disable), (60 bytes).
    Removing stm32f4xx_hal_nor.o(i.HAL_NOR_WriteOperation_Enable), (54 bytes).
    Removing stm32f4xx_hal_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_DeInit), (60 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT), (70 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler), (336 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler), (374 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT), (48 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetError), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetMode), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetState), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Init), (296 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady), (408 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT), (118 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive), (632 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA), (364 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT), (380 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Sequential_Receive_IT), (484 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Sequential_Transmit_IT), (280 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit), (328 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA), (356 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT), (264 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read), (624 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA), (384 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT), (396 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write), (312 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA), (332 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT), (256 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MspInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA), (236 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT), (156 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Sequential_Receive_IT), (128 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Sequential_Transmit_IT), (128 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit), (360 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA), (308 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT), (156 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAError), (50 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt), (90 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt), (92 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAMemReceiveCplt), (90 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAMemTransmitCplt), (92 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt), (128 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt), (98 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed), (52 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF), (300 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE), (238 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead), (304 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite), (180 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF), (178 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE), (42 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead), (328 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite), (212 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_SlaveReceive_BTF), (30 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_SlaveReceive_RXNE), (84 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_SlaveTransmit_BTF), (28 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_SlaveTransmit_TXE), (82 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR), (54 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_AF), (180 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF), (140 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout), (90 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout), (230 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout), (178 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout), (116 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout), (88 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout), (90 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter), (76 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter), (80 bytes).
    Removing stm32f4xx_hal_spi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_spi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_spi.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_DMAPause), (50 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_DMAResume), (50 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_DMAStop), (46 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_DeInit), (54 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_GetError), (6 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_GetState), (8 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_IRQHandler), (222 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_MspInit), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Receive), (368 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA), (232 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT), (204 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_RxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit), (348 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA), (296 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT), (192 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA), (204 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_IT), (184 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT), (52 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT), (50 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT), (52 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT), (50 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_CheckForDisablingSPI), (42 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR), (98 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR), (58 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR), (96 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAError), (40 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt), (14 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt), (14 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt), (14 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt), (76 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt), (114 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt), (90 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_RxISR_16BIT), (36 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_RxISR_8BIT), (34 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_TxISR_16BIT), (36 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_TxISR_8BIT), (34 bytes).

795 unused section(s) (total 96293 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/dclz77c.s                  0x00000000   Number         0  __dclz77c.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi_2.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ..\CMSIS\DeviceSupport\startup_stm32f429xx.s 0x00000000   Number         0  startup_stm32f429xx.o ABSOLUTE
    ..\CMSIS\DeviceSupport\system_stm32f4xx.c 0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma2d.c 0x00000000   Number         0  stm32f4xx_hal_dma2d.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c.c 0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32f4xx_hal_i2c_ex.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_ltdc.c 0x00000000   Number         0  stm32f4xx_hal_ltdc.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_ltdc_ex.c 0x00000000   Number         0  stm32f4xx_hal_ltdc_ex.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_nor.c 0x00000000   Number         0  stm32f4xx_hal_nor.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_sdram.c 0x00000000   Number         0  stm32f4xx_hal_sdram.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_spi.c 0x00000000   Number         0  stm32f4xx_hal_spi.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_sram.c 0x00000000   Number         0  stm32f4xx_hal_sram.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_timebase_tim_template.c 0x00000000   Number         0  stm32f4xx_hal_timebase_tim_template.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_usart.c 0x00000000   Number         0  stm32f4xx_hal_usart.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\Src\stm32f4xx_ll_fmc.c 0x00000000   Number         0  stm32f4xx_ll_fmc.o ABSOLUTE
    ..\STM32F4xx_HAL_Driver\Src\stm32f4xx_ll_fsmc.c 0x00000000   Number         0  stm32f4xx_ll_fsmc.o ABSOLUTE
    ..\User\DAC8563\dac8563.c                0x00000000   Number         0  dac8563.o ABSOLUTE
    ..\User\KEY\key.c                        0x00000000   Number         0  key.o ABSOLUTE
    ..\User\LCD\lcd.c                        0x00000000   Number         0  lcd.o ABSOLUTE
    ..\User\LCD\ltdc.c                       0x00000000   Number         0  ltdc.o ABSOLUTE
    ..\User\LED\led.c                        0x00000000   Number         0  led.o ABSOLUTE
    ..\User\SDRAM\sdram.c                    0x00000000   Number         0  sdram.o ABSOLUTE
    ..\User\SPI\spi.c                        0x00000000   Number         0  spi.o ABSOLUTE
    ..\User\delay\delay.c                    0x00000000   Number         0  delay.o ABSOLUTE
    ..\User\stm32f429_Winner\stm32f429_winner.c 0x00000000   Number         0  stm32f429_winner.o ABSOLUTE
    ..\User\stm32f4xx_it\stm32f4xx_it.c      0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\User\usart\usart.c                    0x00000000   Number         0  usart.o ABSOLUTE
    ..\\CMSIS\\DeviceSupport\\system_stm32f4xx.c 0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma2d.c 0x00000000   Number         0  stm32f4xx_hal_dma2d.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_i2c.c 0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32f4xx_hal_i2c_ex.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_ltdc.c 0x00000000   Number         0  stm32f4xx_hal_ltdc.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_ltdc_ex.c 0x00000000   Number         0  stm32f4xx_hal_ltdc_ex.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_nor.c 0x00000000   Number         0  stm32f4xx_hal_nor.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_sdram.c 0x00000000   Number         0  stm32f4xx_hal_sdram.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_spi.c 0x00000000   Number         0  stm32f4xx_hal_spi.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_sram.c 0x00000000   Number         0  stm32f4xx_hal_sram.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_timebase_tim_template.c 0x00000000   Number         0  stm32f4xx_hal_timebase_tim_template.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_usart.c 0x00000000   Number         0  stm32f4xx_hal_usart.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_ll_fmc.c 0x00000000   Number         0  stm32f4xx_ll_fmc.o ABSOLUTE
    ..\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_ll_fsmc.c 0x00000000   Number         0  stm32f4xx_ll_fsmc.o ABSOLUTE
    ..\\User\\DAC8563\\dac8563.c             0x00000000   Number         0  dac8563.o ABSOLUTE
    ..\\User\\KEY\\key.c                     0x00000000   Number         0  key.o ABSOLUTE
    ..\\User\\LCD\\lcd.c                     0x00000000   Number         0  lcd.o ABSOLUTE
    ..\\User\\LCD\\ltdc.c                    0x00000000   Number         0  ltdc.o ABSOLUTE
    ..\\User\\LED\\led.c                     0x00000000   Number         0  led.o ABSOLUTE
    ..\\User\\SDRAM\\sdram.c                 0x00000000   Number         0  sdram.o ABSOLUTE
    ..\\User\\SPI\\spi.c                     0x00000000   Number         0  spi.o ABSOLUTE
    ..\\User\\delay\\delay.c                 0x00000000   Number         0  delay.o ABSOLUTE
    ..\\User\\stm32f429_Winner\\stm32f429_winner.c 0x00000000   Number         0  stm32f429_winner.o ABSOLUTE
    ..\\User\\stm32f4xx_it\\stm32f4xx_it.c   0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\\User\\usart\\usart.c                 0x00000000   Number         0  usart.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    RESET                                    0x08000000   Section      428  startup_stm32f429xx.o(RESET)
    !!!main                                  0x080001ac   Section        8  __main.o(!!!main)
    !!!scatter                               0x080001b4   Section       52  __scatter.o(!!!scatter)
    !!dclz77c                                0x080001e8   Section      100  __dclz77c.o(!!dclz77c)
    !!handler_zi                             0x0800024c   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x08000268   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x0800026a   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x0800026e   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000270   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000272   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000272   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x08000272   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x08000272   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x08000272   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x08000272   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x08000272   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x08000272   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x08000274   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000274   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000274   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0800027a   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0800027a   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x0800027e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x0800027e   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x08000286   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000288   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000288   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x0800028c   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000294   Section       64  startup_stm32f429xx.o(.text)
    $v0                                      0x08000294   Number         0  startup_stm32f429xx.o(.text)
    .text                                    0x080002d4   Section        2  use_no_semi_2.o(.text)
    .text                                    0x080002d6   Section        0  heapauxi.o(.text)
    .text                                    0x080002dc   Section        2  use_no_semi.o(.text)
    .text                                    0x080002de   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08000328   Section        0  exit.o(.text)
    .text                                    0x0800033c   Section        8  libspace.o(.text)
    i.BSP_LED_Init                           0x08000344   Section        0  led.o(i.BSP_LED_Init)
    i.BusFault_Handler                       0x080003f8   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.DAC8563_Init                           0x080003fc   Section        0  dac8563.o(i.DAC8563_Init)
    i.DAC8563_Write_CHA_CHB                  0x08000460   Section        0  dac8563.o(i.DAC8563_Write_CHA_CHB)
    i.DAC8563_Write_Reg                      0x08000470   Section        0  dac8563.o(i.DAC8563_Write_Reg)
    i.DebugMon_Handler                       0x080004a4   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.HAL_GPIO_Init                          0x080004a8   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_WritePin                      0x080006c4   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x080006d0   Section        0  stm32f4xx_hal.o(i.HAL_GetTick)
    i.HAL_IncTick                            0x080006dc   Section        0  stm32f4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x080006ec   Section        0  stm32f4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08000728   Section        0  stm32f4xx_hal_timebase_tim_template.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x080007c0   Section        0  stm32f4xx_hal.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x080007c2   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x080007dc   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08000858   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_PWREx_EnableOverDrive              0x08000880   Section        0  stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableOverDrive)
    i.HAL_RCC_ClockConfig                    0x08000924   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetClockConfig                 0x08000ac8   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig)
    i.HAL_RCC_GetHCLKFreq                    0x08000b0c   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    i.HAL_RCC_GetPCLK1Freq                   0x08000b44   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetSysClockFreq                0x08000b70   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08000c24   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SPI_Init                           0x08000fc0   Section        0  stm32f4xx_hal_spi.o(i.HAL_SPI_Init)
    i.HAL_SPI_MspInit                        0x0800104c   Section        0  spi.o(i.HAL_SPI_MspInit)
    i.HAL_SPI_TransmitReceive                0x080010c0   Section        0  stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive)
    i.HAL_TIMEx_BreakCallback                0x08001254   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    i.HAL_TIMEx_CommutationCallback          0x08001256   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutationCallback)
    i.HAL_TIM_Base_Init                      0x08001258   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x0800128c   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_Base_Start_IT                  0x0800128e   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    i.HAL_TIM_IC_CaptureCallback             0x080012ac   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    i.HAL_TIM_IRQHandler                     0x080012ae   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    i.HAL_TIM_OC_DelayElapsedCallback        0x08001444   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    i.HAL_TIM_PWM_PulseFinishedCallback      0x08001446   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    i.HAL_TIM_PeriodElapsedCallback          0x08001448   Section        0  stm32f4xx_hal_timebase_tim_template.o(i.HAL_TIM_PeriodElapsedCallback)
    i.HAL_TIM_TriggerCallback                0x08001452   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    i.HAL_UART_ErrorCallback                 0x08001454   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_GetState                      0x08001456   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_GetState)
    i.HAL_UART_IRQHandler                    0x0800146a   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Receive_IT                    0x0800159e   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    i.HAL_UART_RxCpltCallback                0x08001610   Section        0  usart.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_TxCpltCallback                0x08001698   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x0800169a   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.LED_Init                               0x0800169e   Section        0  led.o(i.LED_Init)
    i.MemManage_Handler                      0x080016ae   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x080016b2   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.NVIC_GetPriorityGrouping               0x080016b4   Section        0  stm32f4xx_hal_cortex.o(i.NVIC_GetPriorityGrouping)
    NVIC_GetPriorityGrouping                 0x080016b5   Thumb Code    10  stm32f4xx_hal_cortex.o(i.NVIC_GetPriorityGrouping)
    i.NVIC_SetPriority                       0x080016c4   Section        0  stm32f4xx_hal_cortex.o(i.NVIC_SetPriority)
    NVIC_SetPriority                         0x080016c5   Thumb Code    32  stm32f4xx_hal_cortex.o(i.NVIC_SetPriority)
    i.PendSV_Handler                         0x080016ec   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.SPI5_Init                              0x080016f0   Section        0  spi.o(i.SPI5_Init)
    i.SPI5_Send_Byte                         0x08001738   Section        0  spi.o(i.SPI5_Send_Byte)
    i.SPI_CheckFlag_BSY                      0x08001754   Section        0  stm32f4xx_hal_spi.o(i.SPI_CheckFlag_BSY)
    SPI_CheckFlag_BSY                        0x08001755   Thumb Code    40  stm32f4xx_hal_spi.o(i.SPI_CheckFlag_BSY)
    i.SPI_WaitFlagStateUntilTimeout          0x0800177c   Section        0  stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout)
    SPI_WaitFlagStateUntilTimeout            0x0800177d   Thumb Code   160  stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout)
    i.SVC_Handler                            0x0800181c   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.SysTick_Handler                        0x0800181e   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08001828   Section        0  stm32f429_winner.o(i.SystemClock_Config)
    i.SystemInit                             0x080018e0   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.TIM6_DAC_IRQHandler                    0x08001944   Section        0  stm32f4xx_hal_timebase_tim_template.o(i.TIM6_DAC_IRQHandler)
    i.TIM_Base_SetConfig                     0x08001954   Section        0  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.UART_EndTransmit_IT                    0x08001a38   Section        0  stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT)
    UART_EndTransmit_IT                      0x08001a39   Thumb Code    32  stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT)
    i.UART_Receive_IT                        0x08001a58   Section        0  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x08001a59   Thumb Code   166  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_Transmit_IT                       0x08001afe   Section        0  stm32f4xx_hal_uart.o(i.UART_Transmit_IT)
    UART_Transmit_IT                         0x08001aff   Thumb Code   104  stm32f4xx_hal_uart.o(i.UART_Transmit_IT)
    i.USART1_IRQHandler                      0x08001b68   Section        0  usart.o(i.USART1_IRQHandler)
    i.UsageFault_Handler                     0x08001bb8   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i._sys_exit                              0x08001bbc   Section        0  usart.o(i._sys_exit)
    i.main                                   0x08001bc0   Section        0  main.o(i.main)
    x$fpl$fpinit                             0x08001bfc   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x08001bfc   Number         0  fpinit.o(x$fpl$fpinit)
    .constdata                               0x08001c06   Section        6  led.o(.constdata)
    .constdata                               0x08001c0c   Section       16  stm32f4xx_hal_rcc.o(.constdata)
    .data                                    0x20000000   Section      512  main.o(.data)
    .data                                    0x20000200   Section       20  system_stm32f4xx.o(.data)
    .data                                    0x20000214   Section       12  led.o(.data)
    .data                                    0x20000220   Section        8  usart.o(.data)
    .data                                    0x20000228   Section        4  stm32f4xx_hal.o(.data)
    .bss                                     0x2000022c   Section      264  usart.o(.bss)
    .bss                                     0x20000334   Section       88  spi.o(.bss)
    .bss                                     0x2000038c   Section       60  stm32f4xx_hal_timebase_tim_template.o(.bss)
    .bss                                     0x200003c8   Section       96  libspace.o(.bss)
    HEAP                                     0x20000428   Section      512  startup_stm32f429xx.o(HEAP)
    Heap_Mem                                 0x20000428   Data         512  startup_stm32f429xx.o(HEAP)
    STACK                                    0x20000628   Section     1024  startup_stm32f429xx.o(STACK)
    Stack_Mem                                0x20000628   Data        1024  startup_stm32f429xx.o(STACK)
    __initial_sp                             0x20000a28   Data           0  startup_stm32f429xx.o(STACK)
    .ARM.__AT_0xC0000000                     0xc0000000   Section    1228800  ltdc.o(.ARM.__AT_0xC0000000)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x000001ac   Number         0  startup_stm32f429xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f429xx.o(RESET)
    __Vectors_End                            0x080001ac   Data           0  startup_stm32f429xx.o(RESET)
    __main                                   0x080001ad   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080001b5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080001b5   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080001b5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x080001c3   Thumb Code     0  __scatter.o(!!!scatter)
    __decompress                             0x080001e9   Thumb Code   100  __dclz77c.o(!!dclz77c)
    __decompress2                            0x080001e9   Thumb Code     0  __dclz77c.o(!!dclz77c)
    __scatterload_zeroinit                   0x0800024d   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x08000269   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x0800026b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_alloca_1                   0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x08000271   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000273   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x08000273   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000273   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x08000273   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x08000273   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x08000273   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x08000273   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x08000273   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x08000275   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000275   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000275   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0800027b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0800027b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x0800027f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x0800027f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x08000287   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000289   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000289   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x0800028d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000295   Thumb Code     8  startup_stm32f429xx.o(.text)
    ADC_IRQHandler                           0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN1_RX0_IRQHandler                      0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN1_RX1_IRQHandler                      0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN1_SCE_IRQHandler                      0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN1_TX_IRQHandler                       0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN2_RX0_IRQHandler                      0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN2_RX1_IRQHandler                      0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN2_SCE_IRQHandler                      0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN2_TX_IRQHandler                       0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    DCMI_IRQHandler                          0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2D_IRQHandler                         0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream2_IRQHandler                  0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    ETH_IRQHandler                           0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    ETH_WKUP_IRQHandler                      0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI0_IRQHandler                         0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI15_10_IRQHandler                     0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI1_IRQHandler                         0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI2_IRQHandler                         0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI3_IRQHandler                         0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI4_IRQHandler                         0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI9_5_IRQHandler                       0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    FLASH_IRQHandler                         0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    FMC_IRQHandler                           0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    FPU_IRQHandler                           0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    HASH_RNG_IRQHandler                      0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C1_ER_IRQHandler                       0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C1_EV_IRQHandler                       0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C2_ER_IRQHandler                       0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C2_EV_IRQHandler                       0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C3_ER_IRQHandler                       0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C3_EV_IRQHandler                       0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    LTDC_ER_IRQHandler                       0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    LTDC_IRQHandler                          0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_FS_IRQHandler                        0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_HS_IRQHandler                        0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    PVD_IRQHandler                           0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    RCC_IRQHandler                           0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    RTC_Alarm_IRQHandler                     0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    RTC_WKUP_IRQHandler                      0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    SAI1_IRQHandler                          0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    SDIO_IRQHandler                          0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI1_IRQHandler                          0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI2_IRQHandler                          0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI3_IRQHandler                          0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI4_IRQHandler                          0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI5_IRQHandler                          0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI6_IRQHandler                          0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM1_CC_IRQHandler                       0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM2_IRQHandler                          0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM3_IRQHandler                          0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM4_IRQHandler                          0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM5_IRQHandler                          0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM7_IRQHandler                          0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM8_CC_IRQHandler                       0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    UART4_IRQHandler                         0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    UART5_IRQHandler                         0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    UART7_IRQHandler                         0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    UART8_IRQHandler                         0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    USART2_IRQHandler                        0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    USART3_IRQHandler                        0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    USART6_IRQHandler                        0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    WWDG_IRQHandler                          0x080002af   Thumb Code     0  startup_stm32f429xx.o(.text)
    __user_initial_stackheap                 0x080002b1   Thumb Code     0  startup_stm32f429xx.o(.text)
    __use_no_semihosting                     0x080002d5   Thumb Code     2  use_no_semi_2.o(.text)
    __use_two_region_memory                  0x080002d7   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x080002d9   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x080002db   Thumb Code     2  heapauxi.o(.text)
    __I$use$semihosting                      0x080002dd   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x080002dd   Thumb Code     2  use_no_semi.o(.text)
    __user_setup_stackheap                   0x080002df   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08000329   Thumb Code    18  exit.o(.text)
    __user_libspace                          0x0800033d   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x0800033d   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x0800033d   Thumb Code     0  libspace.o(.text)
    BSP_LED_Init                             0x08000345   Thumb Code   168  led.o(i.BSP_LED_Init)
    BusFault_Handler                         0x080003f9   Thumb Code     4  stm32f4xx_it.o(i.BusFault_Handler)
    DAC8563_Init                             0x080003fd   Thumb Code    90  dac8563.o(i.DAC8563_Init)
    DAC8563_Write_CHA_CHB                    0x08000461   Thumb Code    14  dac8563.o(i.DAC8563_Write_CHA_CHB)
    DAC8563_Write_Reg                        0x08000471   Thumb Code    46  dac8563.o(i.DAC8563_Write_Reg)
    DebugMon_Handler                         0x080004a5   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    HAL_GPIO_Init                            0x080004a9   Thumb Code   486  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_WritePin                        0x080006c5   Thumb Code    12  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x080006d1   Thumb Code     6  stm32f4xx_hal.o(i.HAL_GetTick)
    HAL_IncTick                              0x080006dd   Thumb Code    12  stm32f4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x080006ed   Thumb Code    54  stm32f4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08000729   Thumb Code   134  stm32f4xx_hal_timebase_tim_template.o(i.HAL_InitTick)
    HAL_MspInit                              0x080007c1   Thumb Code     2  stm32f4xx_hal.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x080007c3   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x080007dd   Thumb Code   124  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08000859   Thumb Code    32  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_PWREx_EnableOverDrive                0x08000881   Thumb Code   152  stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableOverDrive)
    HAL_RCC_ClockConfig                      0x08000925   Thumb Code   412  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetClockConfig                   0x08000ac9   Thumb Code    58  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig)
    HAL_RCC_GetHCLKFreq                      0x08000b0d   Thumb Code    42  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    HAL_RCC_GetPCLK1Freq                     0x08000b45   Thumb Code    36  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetSysClockFreq                  0x08000b71   Thumb Code   168  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08000c25   Thumb Code   906  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SPI_Init                             0x08000fc1   Thumb Code   140  stm32f4xx_hal_spi.o(i.HAL_SPI_Init)
    HAL_SPI_MspInit                          0x0800104d   Thumb Code   108  spi.o(i.HAL_SPI_MspInit)
    HAL_SPI_TransmitReceive                  0x080010c1   Thumb Code   404  stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive)
    HAL_TIMEx_BreakCallback                  0x08001255   Thumb Code     2  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    HAL_TIMEx_CommutationCallback            0x08001257   Thumb Code     2  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutationCallback)
    HAL_TIM_Base_Init                        0x08001259   Thumb Code    52  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x0800128d   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_Base_Start_IT                    0x0800128f   Thumb Code    30  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    HAL_TIM_IC_CaptureCallback               0x080012ad   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    HAL_TIM_IRQHandler                       0x080012af   Thumb Code   406  stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    HAL_TIM_OC_DelayElapsedCallback          0x08001445   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    HAL_TIM_PWM_PulseFinishedCallback        0x08001447   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    HAL_TIM_PeriodElapsedCallback            0x08001449   Thumb Code    10  stm32f4xx_hal_timebase_tim_template.o(i.HAL_TIM_PeriodElapsedCallback)
    HAL_TIM_TriggerCallback                  0x08001453   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    HAL_UART_ErrorCallback                   0x08001455   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_GetState                        0x08001457   Thumb Code    20  stm32f4xx_hal_uart.o(i.HAL_UART_GetState)
    HAL_UART_IRQHandler                      0x0800146b   Thumb Code   308  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Receive_IT                      0x0800159f   Thumb Code   112  stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    HAL_UART_RxCpltCallback                  0x08001611   Thumb Code   120  usart.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_TxCpltCallback                  0x08001699   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x0800169b   Thumb Code     4  stm32f4xx_it.o(i.HardFault_Handler)
    LED_Init                                 0x0800169f   Thumb Code    16  led.o(i.LED_Init)
    MemManage_Handler                        0x080016af   Thumb Code     4  stm32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x080016b3   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    PendSV_Handler                           0x080016ed   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    SPI5_Init                                0x080016f1   Thumb Code    64  spi.o(i.SPI5_Init)
    SPI5_Send_Byte                           0x08001739   Thumb Code    24  spi.o(i.SPI5_Send_Byte)
    SVC_Handler                              0x0800181d   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x0800181f   Thumb Code     8  stm32f4xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x08001829   Thumb Code   176  stm32f429_winner.o(i.SystemClock_Config)
    SystemInit                               0x080018e1   Thumb Code    82  system_stm32f4xx.o(i.SystemInit)
    TIM6_DAC_IRQHandler                      0x08001945   Thumb Code    10  stm32f4xx_hal_timebase_tim_template.o(i.TIM6_DAC_IRQHandler)
    TIM_Base_SetConfig                       0x08001955   Thumb Code   184  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    USART1_IRQHandler                        0x08001b69   Thumb Code    66  usart.o(i.USART1_IRQHandler)
    UsageFault_Handler                       0x08001bb9   Thumb Code     4  stm32f4xx_it.o(i.UsageFault_Handler)
    _sys_exit                                0x08001bbd   Thumb Code     4  usart.o(i._sys_exit)
    main                                     0x08001bc1   Thumb Code    54  main.o(i.main)
    _fp_init                                 0x08001bfd   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x08001c05   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x08001c05   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    GPIO_PIN                                 0x08001c06   Data           6  led.o(.constdata)
    APBAHBPrescTable                         0x08001c0c   Data          16  stm32f4xx_hal_rcc.o(.constdata)
    Region$$Table$$Base                      0x08001c1c   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08001c3c   Number         0  anon$$obj.o(Region$$Table)
    sin_data                                 0x20000000   Data         512  main.o(.data)
    SystemCoreClock                          0x20000200   Data           4  system_stm32f4xx.o(.data)
    AHBPrescTable                            0x20000204   Data          16  system_stm32f4xx.o(.data)
    GPIO_PORT                                0x20000214   Data          12  led.o(.data)
    USART_RX_STA                             0x20000220   Data           2  usart.o(.data)
    aRxBuffer                                0x20000222   Data           1  usart.o(.data)
    __stdout                                 0x20000224   Data           4  usart.o(.data)
    uwTick                                   0x20000228   Data           4  stm32f4xx_hal.o(.data)
    USART_RX_BUF                             0x2000022c   Data         200  usart.o(.bss)
    UART_Handler                             0x200002f4   Data          64  usart.o(.bss)
    SPI5_Handler                             0x20000334   Data          88  spi.o(.bss)
    TimHandle                                0x2000038c   Data          60  stm32f4xx_hal_timebase_tim_template.o(.bss)
    __libspace_start                         0x200003c8   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000428   Data           0  libspace.o(.bss)
    ltdc_lcd_framebuf                        0xc0000000   Data       1228800  ltdc.o(.ARM.__AT_0xC0000000)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080001ad

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00001e68, Max: 0x00100000, ABSOLUTE, COMPRESSED[0x00001e58])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00001c3c, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000001ac   Data   RO         5492    RESET               startup_stm32f429xx.o
    0x080001ac   0x080001ac   0x00000008   Code   RO         5505  * !!!main             c_w.l(__main.o)
    0x080001b4   0x080001b4   0x00000034   Code   RO         5664    !!!scatter          c_w.l(__scatter.o)
    0x080001e8   0x080001e8   0x00000064   Code   RO         5662    !!dclz77c           c_w.l(__dclz77c.o)
    0x0800024c   0x0800024c   0x0000001c   Code   RO         5666    !!handler_zi        c_w.l(__scatter_zi.o)
    0x08000268   0x08000268   0x00000002   Code   RO         5534    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x0800026a   0x0800026a   0x00000004   Code   RO         5540    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         5543    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         5546    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         5548    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         5550    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         5553    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         5555    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         5557    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         5559    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         5561    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         5563    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         5565    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         5567    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         5569    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         5571    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         5573    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         5577    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         5579    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         5581    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         5583    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000002   Code   RO         5584    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000270   0x08000270   0x00000002   Code   RO         5602    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000272   0x08000272   0x00000000   Code   RO         5612    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000272   0x08000272   0x00000000   Code   RO         5614    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000272   0x08000272   0x00000000   Code   RO         5616    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x08000272   0x08000272   0x00000000   Code   RO         5619    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x08000272   0x08000272   0x00000000   Code   RO         5622    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000272   0x08000272   0x00000000   Code   RO         5624    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x08000272   0x08000272   0x00000000   Code   RO         5627    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x08000272   0x08000272   0x00000002   Code   RO         5628    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x08000274   0x08000274   0x00000000   Code   RO         5509    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000274   0x08000274   0x00000000   Code   RO         5511    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000274   0x08000274   0x00000006   Code   RO         5523    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x0800027a   0x0800027a   0x00000000   Code   RO         5513    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x0800027a   0x0800027a   0x00000004   Code   RO         5514    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x0800027e   0x0800027e   0x00000000   Code   RO         5516    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x0800027e   0x0800027e   0x00000008   Code   RO         5517    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x08000286   0x08000286   0x00000002   Code   RO         5538    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000288   0x08000288   0x00000000   Code   RO         5586    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000288   0x08000288   0x00000004   Code   RO         5587    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x0800028c   0x0800028c   0x00000006   Code   RO         5588    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000292   0x08000292   0x00000002   PAD
    0x08000294   0x08000294   0x00000040   Code   RO         5493    .text               startup_stm32f429xx.o
    0x080002d4   0x080002d4   0x00000002   Code   RO         5499    .text               c_w.l(use_no_semi_2.o)
    0x080002d6   0x080002d6   0x00000006   Code   RO         5503    .text               c_w.l(heapauxi.o)
    0x080002dc   0x080002dc   0x00000002   Code   RO         5507    .text               c_w.l(use_no_semi.o)
    0x080002de   0x080002de   0x0000004a   Code   RO         5525    .text               c_w.l(sys_stackheap_outer.o)
    0x08000328   0x08000328   0x00000012   Code   RO         5527    .text               c_w.l(exit.o)
    0x0800033a   0x0800033a   0x00000002   PAD
    0x0800033c   0x0800033c   0x00000008   Code   RO         5535    .text               c_w.l(libspace.o)
    0x08000344   0x08000344   0x000000b4   Code   RO          489    i.BSP_LED_Init      led.o
    0x080003f8   0x080003f8   0x00000004   Code   RO          279    i.BusFault_Handler  stm32f4xx_it.o
    0x080003fc   0x080003fc   0x00000064   Code   RO         1142    i.DAC8563_Init      dac8563.o
    0x08000460   0x08000460   0x0000000e   Code   RO         1144    i.DAC8563_Write_CHA_CHB  dac8563.o
    0x0800046e   0x0800046e   0x00000002   PAD
    0x08000470   0x08000470   0x00000034   Code   RO         1146    i.DAC8563_Write_Reg  dac8563.o
    0x080004a4   0x080004a4   0x00000002   Code   RO          280    i.DebugMon_Handler  stm32f4xx_it.o
    0x080004a6   0x080004a6   0x00000002   PAD
    0x080004a8   0x080004a8   0x0000021c   Code   RO         1499    i.HAL_GPIO_Init     stm32f4xx_hal_gpio.o
    0x080006c4   0x080006c4   0x0000000c   Code   RO         1503    i.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x080006d0   0x080006d0   0x0000000c   Code   RO         1216    i.HAL_GetTick       stm32f4xx_hal.o
    0x080006dc   0x080006dc   0x00000010   Code   RO         1217    i.HAL_IncTick       stm32f4xx_hal.o
    0x080006ec   0x080006ec   0x0000003c   Code   RO         1218    i.HAL_Init          stm32f4xx_hal.o
    0x08000728   0x08000728   0x00000098   Code   RO         3584    i.HAL_InitTick      stm32f4xx_hal_timebase_tim_template.o
    0x080007c0   0x080007c0   0x00000002   Code   RO         1221    i.HAL_MspInit       stm32f4xx_hal.o
    0x080007c2   0x080007c2   0x0000001a   Code   RO         1367    i.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x080007dc   0x080007dc   0x0000007c   Code   RO         1373    i.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08000858   0x08000858   0x00000028   Code   RO         1374    i.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x08000880   0x08000880   0x000000a4   Code   RO         1696    i.HAL_PWREx_EnableOverDrive  stm32f4xx_hal_pwr_ex.o
    0x08000924   0x08000924   0x000001a4   Code   RO         1767    i.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x08000ac8   0x08000ac8   0x00000044   Code   RO         1771    i.HAL_RCC_GetClockConfig  stm32f4xx_hal_rcc.o
    0x08000b0c   0x08000b0c   0x00000038   Code   RO         1772    i.HAL_RCC_GetHCLKFreq  stm32f4xx_hal_rcc.o
    0x08000b44   0x08000b44   0x0000002c   Code   RO         1774    i.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x08000b70   0x08000b70   0x000000b4   Code   RO         1776    i.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x08000c24   0x08000c24   0x0000039c   Code   RO         1779    i.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x08000fc0   0x08000fc0   0x0000008c   Code   RO         5218    i.HAL_SPI_Init      stm32f4xx_hal_spi.o
    0x0800104c   0x0800104c   0x00000074   Code   RO         1046    i.HAL_SPI_MspInit   spi.o
    0x080010c0   0x080010c0   0x00000194   Code   RO         5227    i.HAL_SPI_TransmitReceive  stm32f4xx_hal_spi.o
    0x08001254   0x08001254   0x00000002   Code   RO         3341    i.HAL_TIMEx_BreakCallback  stm32f4xx_hal_tim_ex.o
    0x08001256   0x08001256   0x00000002   Code   RO         3342    i.HAL_TIMEx_CommutationCallback  stm32f4xx_hal_tim_ex.o
    0x08001258   0x08001258   0x00000034   Code   RO         2698    i.HAL_TIM_Base_Init  stm32f4xx_hal_tim.o
    0x0800128c   0x0800128c   0x00000002   Code   RO         2700    i.HAL_TIM_Base_MspInit  stm32f4xx_hal_tim.o
    0x0800128e   0x0800128e   0x0000001e   Code   RO         2703    i.HAL_TIM_Base_Start_IT  stm32f4xx_hal_tim.o
    0x080012ac   0x080012ac   0x00000002   Code   RO         2727    i.HAL_TIM_IC_CaptureCallback  stm32f4xx_hal_tim.o
    0x080012ae   0x080012ae   0x00000196   Code   RO         2740    i.HAL_TIM_IRQHandler  stm32f4xx_hal_tim.o
    0x08001444   0x08001444   0x00000002   Code   RO         2743    i.HAL_TIM_OC_DelayElapsedCallback  stm32f4xx_hal_tim.o
    0x08001446   0x08001446   0x00000002   Code   RO         2770    i.HAL_TIM_PWM_PulseFinishedCallback  stm32f4xx_hal_tim.o
    0x08001448   0x08001448   0x0000000a   Code   RO         3587    i.HAL_TIM_PeriodElapsedCallback  stm32f4xx_hal_timebase_tim_template.o
    0x08001452   0x08001452   0x00000002   Code   RO         2781    i.HAL_TIM_TriggerCallback  stm32f4xx_hal_tim.o
    0x08001454   0x08001454   0x00000002   Code   RO         3651    i.HAL_UART_ErrorCallback  stm32f4xx_hal_uart.o
    0x08001456   0x08001456   0x00000014   Code   RO         3653    i.HAL_UART_GetState  stm32f4xx_hal_uart.o
    0x0800146a   0x0800146a   0x00000134   Code   RO         3654    i.HAL_UART_IRQHandler  stm32f4xx_hal_uart.o
    0x0800159e   0x0800159e   0x00000070   Code   RO         3660    i.HAL_UART_Receive_IT  stm32f4xx_hal_uart.o
    0x0800160e   0x0800160e   0x00000002   PAD
    0x08001610   0x08001610   0x00000088   Code   RO          979    i.HAL_UART_RxCpltCallback  usart.o
    0x08001698   0x08001698   0x00000002   Code   RO         3666    i.HAL_UART_TxCpltCallback  stm32f4xx_hal_uart.o
    0x0800169a   0x0800169a   0x00000004   Code   RO          281    i.HardFault_Handler  stm32f4xx_it.o
    0x0800169e   0x0800169e   0x00000010   Code   RO          490    i.LED_Init          led.o
    0x080016ae   0x080016ae   0x00000004   Code   RO          282    i.MemManage_Handler  stm32f4xx_it.o
    0x080016b2   0x080016b2   0x00000002   Code   RO          283    i.NMI_Handler       stm32f4xx_it.o
    0x080016b4   0x080016b4   0x00000010   Code   RO         1380    i.NVIC_GetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x080016c4   0x080016c4   0x00000028   Code   RO         1381    i.NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x080016ec   0x080016ec   0x00000002   Code   RO          284    i.PendSV_Handler    stm32f4xx_it.o
    0x080016ee   0x080016ee   0x00000002   PAD
    0x080016f0   0x080016f0   0x00000048   Code   RO         1047    i.SPI5_Init         spi.o
    0x08001738   0x08001738   0x0000001c   Code   RO         1048    i.SPI5_Send_Byte    spi.o
    0x08001754   0x08001754   0x00000028   Code   RO         5240    i.SPI_CheckFlag_BSY  stm32f4xx_hal_spi.o
    0x0800177c   0x0800177c   0x000000a0   Code   RO         5256    i.SPI_WaitFlagStateUntilTimeout  stm32f4xx_hal_spi.o
    0x0800181c   0x0800181c   0x00000002   Code   RO          285    i.SVC_Handler       stm32f4xx_it.o
    0x0800181e   0x0800181e   0x00000008   Code   RO          286    i.SysTick_Handler   stm32f4xx_it.o
    0x08001826   0x08001826   0x00000002   PAD
    0x08001828   0x08001828   0x000000b8   Code   RO          401    i.SystemClock_Config  stm32f429_winner.o
    0x080018e0   0x080018e0   0x00000064   Code   RO          362    i.SystemInit        system_stm32f4xx.o
    0x08001944   0x08001944   0x00000010   Code   RO         3588    i.TIM6_DAC_IRQHandler  stm32f4xx_hal_timebase_tim_template.o
    0x08001954   0x08001954   0x000000e4   Code   RO         2782    i.TIM_Base_SetConfig  stm32f4xx_hal_tim.o
    0x08001a38   0x08001a38   0x00000020   Code   RO         3673    i.UART_EndTransmit_IT  stm32f4xx_hal_uart.o
    0x08001a58   0x08001a58   0x000000a6   Code   RO         3674    i.UART_Receive_IT   stm32f4xx_hal_uart.o
    0x08001afe   0x08001afe   0x00000068   Code   RO         3676    i.UART_Transmit_IT  stm32f4xx_hal_uart.o
    0x08001b66   0x08001b66   0x00000002   PAD
    0x08001b68   0x08001b68   0x00000050   Code   RO          980    i.USART1_IRQHandler  usart.o
    0x08001bb8   0x08001bb8   0x00000004   Code   RO          287    i.UsageFault_Handler  stm32f4xx_it.o
    0x08001bbc   0x08001bbc   0x00000004   Code   RO          981    i._sys_exit         usart.o
    0x08001bc0   0x08001bc0   0x0000003c   Code   RO            4    i.main              main.o
    0x08001bfc   0x08001bfc   0x0000000a   Code   RO         5594    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x08001c06   0x08001c06   0x00000006   Data   RO          491    .constdata          led.o
    0x08001c0c   0x08001c0c   0x00000010   Data   RO         1780    .constdata          stm32f4xx_hal_rcc.o
    0x08001c1c   0x08001c1c   0x00000020   Data   RO         5660    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08001c3c, Size: 0x00000a28, Max: 0x00030000, ABSOLUTE, COMPRESSED[0x0000021c])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x00000200   Data   RW            5    .data               main.o
    0x20000200   COMPRESSED   0x00000014   Data   RW          363    .data               system_stm32f4xx.o
    0x20000214   COMPRESSED   0x0000000c   Data   RW          492    .data               led.o
    0x20000220   COMPRESSED   0x00000008   Data   RW          985    .data               usart.o
    0x20000228   COMPRESSED   0x00000004   Data   RW         1224    .data               stm32f4xx_hal.o
    0x2000022c        -       0x00000108   Zero   RW          984    .bss                usart.o
    0x20000334        -       0x00000058   Zero   RW         1050    .bss                spi.o
    0x2000038c        -       0x0000003c   Zero   RW         3589    .bss                stm32f4xx_hal_timebase_tim_template.o
    0x200003c8        -       0x00000060   Zero   RW         5536    .bss                c_w.l(libspace.o)
    0x20000428        -       0x00000200   Zero   RW         5491    HEAP                startup_stm32f429xx.o
    0x20000628        -       0x00000400   Zero   RW         5490    STACK               startup_stm32f429xx.o



  Load Region LR$$.ARM.__AT_0xC0000000 (Base: 0xc0000000, Size: 0x00000000, Max: 0x0012c000, ABSOLUTE)

    Execution Region ER$$.ARM.__AT_0xC0000000 (Exec base: 0xc0000000, Load base: 0xc0000000, Size: 0x0012c000, Max: 0x0012c000, ABSOLUTE, UNINIT)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0xc0000000        -       0x0012c000   Zero   RW          790    .ARM.__AT_0xC0000000  ltdc.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       166         16          0          0          0       2679   dac8563.o
       196         12          6         12          0       2259   led.o
         0          0          0          0    1228800       2373   ltdc.o
        60          6          0        512          0     431262   main.o
       216         20          0          0         88       2854   spi.o
        64         26        428          0       1536        956   startup_stm32f429xx.o
       184          8          0          0          0       1693   stm32f429_winner.o
        90         16          0          4          0       4206   stm32f4xx_hal.o
       246         22          0          0          0      30730   stm32f4xx_hal_cortex.o
       552         54          0          0          0       2828   stm32f4xx_hal_gpio.o
       164         12          0          0          0       1588   stm32f4xx_hal_pwr_ex.o
      1692         70         16          0          0       6677   stm32f4xx_hal_rcc.o
       744          0          0          0          0       4609   stm32f4xx_hal_spi.o
       726         44          0          0          0       7334   stm32f4xx_hal_tim.o
         4          0          0          0          0       1982   stm32f4xx_hal_tim_ex.o
       178         24          0          0         60       3569   stm32f4xx_hal_timebase_tim_template.o
       746          0          0          0          0       6991   stm32f4xx_hal_uart.o
        32          0          0          0          0       5386   stm32f4xx_it.o
       100         18          0         20          0       1621   system_stm32f4xx.o
       220         30          0          8        264       4369   usart.o

    ----------------------------------------------------------------------
      6392        <USER>        <GROUP>        556    1230748     525966   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        12          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       100          0          0          0          0          0   __dclz77c.o
         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        28          0          0          0          0          0   __scatter_zi.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         2          0          0          0          0          0   libinit.o
         6          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
         2          0          0          0          0         68   use_no_semi_2.o
        10          0          0          0          0        116   fpinit.o

    ----------------------------------------------------------------------
       354         <USER>          <GROUP>          0         96        700   Library Totals
         4          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       340         12          0          0         96        584   c_w.l
        10          0          0          0          0        116   fz_wm.l

    ----------------------------------------------------------------------
       354         <USER>          <GROUP>          0         96        700   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      6746        390        482        556    1230844     520410   Grand Totals
      6746        390        482        540    1230844     520410   ELF Image Totals (compressed)
      6746        390        482        540          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 7228 (   7.06kB)
    Total RW  Size (RW Data + ZI Data)           1231400 (1202.54kB)
    Total ROM Size (Code + RO Data + RW Data)       7768 (   7.59kB)

==============================================================================

